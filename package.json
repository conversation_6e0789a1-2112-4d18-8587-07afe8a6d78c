{"name": "lobe-console", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@ant-design/plots": "^2.6.1", "@ant-design/pro-components": "^2.8.10", "@ant-design/pro-layout": "^7.22.7", "@ant-design/v5-patch-for-react-19": "^1.0.3", "@clerk/nextjs": "^6.25.4", "@clerk/themes": "^2.3.3", "@clerk/types": "^4.68.0", "@hono/zod-validator": "^0.7.2", "@lobehub/ui": "^2.7.4", "@neondatabase/serverless": "^1.0.1", "@t3-oss/env-nextjs": "^0.13.8", "@tanstack/react-query": "^5.83.0", "@trpc/client": "^11.4.3", "@trpc/react-query": "^11.4.3", "@trpc/server": "^11.4.3", "ahooks": "^3.9.0", "antd": "^5.26.6", "antd-style": "^3.7.1", "dayjs": "^1.11.13", "drizzle-kit": "^0.30.4", "drizzle-orm": "^0.44.3", "drizzle-zod": "^0.8.2", "hono": "^4.8.5", "lodash-es": "^4.17.21", "lucide-react": "^0.525.0", "next": "15.4.2", "nextjs-toploader": "^3.8.16", "nuqs": "^2.4.3", "openapi-fetch": "^0.14.0", "pino": "^9.7.0", "query-string": "^9.2.2", "react": "19.1.0", "react-dom": "19.1.0", "react-layout-kit": "^1.9.2", "superjson": "^2.2.2", "url-join": "^5.0.0", "uuid": "^11.1.0", "ws": "^8.18.3", "zod": "^4.0.5", "zustand": "^5.0.6", "zustand-utils": "^2.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@lobehub/lint": "^1.25.5", "@next/eslint-plugin-next": "^15.1.6", "@types/lodash-es": "^4.17.12", "@types/node": "^22.12.0", "@types/pg": "^8.11.11", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@types/uuid": "^10.0.0", "@types/ws": "^8.18.1", "dotenv": "^16.4.7", "eslint": "^8.57.1", "eslint-config-next": "15.4.2", "husky": "^9.1.7", "lint-staged": "^15.4.3", "openapi-typescript": "^7.6.0", "prettier": "^3.4.2", "stylelint": "^15.11.0", "typescript": "^5.7.3", "vitest": "^3.2.4"}}