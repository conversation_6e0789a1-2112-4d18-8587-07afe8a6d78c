import { router } from '@/libs/trpc';
import { featureRouter } from './feature';
import { licenseRouter } from './license';
import { organizationRouter } from './organization';
import { versionRouter } from './version';

export const appRouter = router({
  feature: featureRouter,
  license: licenseRouter,
  organization: organizationRouter,
  version: versionRouter,
});

export type AppRouter = typeof appRouter;
