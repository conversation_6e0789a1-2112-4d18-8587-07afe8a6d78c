import { z } from 'zod';

import { authedProcedure, router } from '@/libs/trpc';
import { VersionModel } from '@/database/models/version';
import {
  CreateVersionSchema,
  UpdateVersionSchema,
  CreateVersionFeatureSchema,
  VersionQuerySchema,
} from '@/types/version';

const versionProcedure = authedProcedure.use(async (opts) => {
  return opts.next({
    ctx: {
      versionModel: new VersionModel(),
    },
  });
});

export const versionRouter = router({
  // 根据条件查询版本
  queryVersions: versionProcedure
    .input(VersionQuerySchema)
    .query(async ({ ctx, input }) => {
      const result = await ctx.versionModel.queryVersions(input);

      return {
        success: true,
        data: result,
      };
    }),

  // 获取版本详细
  getVersion: versionProcedure
    .input(z.string())
    .query(async ({ ctx, input }) => {
      const result = await ctx.versionModel.queryVersion({ id: input });

      return {
        success: true,
        data: result,
      };
    }),

  // 获取版本及其关联的功能
  getVersionWithFeatures: versionProcedure
    .input(z.string())
    .query(async ({ ctx, input }) => {
      const result = await ctx.versionModel.queryVersionWithFeatures(input);

      return {
        success: true,
        data: result,
      };
    }),

  // 创建版本
  createVersion: versionProcedure
    .input(CreateVersionSchema)
    .mutation(async ({ ctx, input }) => {
      const result = await ctx.versionModel.createVersion(input);

      return {
        success: true,
        data: result,
      };
    }),

  // 更新版本信息
  updateVersion: versionProcedure
    .input(
      z.object({
        id: z.string(),
        data: UpdateVersionSchema,
      })
    )
    .mutation(async ({ ctx, input }) => {
      const result = await ctx.versionModel.updateVersion(input.id, input.data);

      return {
        success: true,
        data: result,
      };
    }),

  // 删除版本
  deleteVersion: versionProcedure
    .input(z.string())
    .mutation(async ({ ctx, input }) => {
      const result = await ctx.versionModel.deleteVersion(input);

      return {
        success: result,
        data: result,
      };
    }),

  // 批量删除版本
  batchDeleteVersions: versionProcedure
    .input(z.object({ versionIds: z.array(z.string()) }))
    .mutation(async ({ ctx, input }) => {
      const result = await ctx.versionModel.batchDeleteVersions(
        input.versionIds
      );

      return {
        success: result,
        data: result,
      };
    }),

  // === 版本功能关联相关接口 ===

  // 创建版本和功能的关联关系
  relateVersionFeatures: versionProcedure
    .input(CreateVersionFeatureSchema)
    .mutation(async ({ ctx, input }) => {
      // 先删除版本关联的全部功能
      await ctx.versionModel.deleteAllVersionFeatures(input.versionId);

      // 构造版本功能关联数据
      const versionFeatures = [
        ...(input.grantFeatures?.map((featureId) => ({
          versionId: input.versionId,
          featureId,
          grantType: 'grant',
        })) ?? []),
        ...(input.revokeFeatures?.map((featureId) => ({
          versionId: input.versionId,
          featureId,
          grantType: 'revoke',
        })) ?? []),
      ];

      // 创建版本功能关联
      await ctx.versionModel.batchCreateVersionFeatures(versionFeatures);

      return {
        success: true,
        data: null,
      };
    }),

  // 获取版本关联的功能
  getVersionFeatures: versionProcedure
    .input(z.string())
    .query(async ({ ctx, input }) => {
      const result = await ctx.versionModel.getVersionFeatures(input);

      return {
        success: true,
        data: result,
      };
    }),

  // 获取所有版本列表
  getAllVersions: versionProcedure.query(async ({ ctx }) => {
    const result = await ctx.versionModel.getAllVersions();

    return {
      success: true,
      data: result,
    };
  }),
});
