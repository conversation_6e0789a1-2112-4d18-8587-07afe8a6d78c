import { string, z } from 'zod';

import { authedProcedure, router } from '@/libs/trpc';
import { OrganizationModel } from '@/database/models';
import {
  OrganizationQuerySchema,
  CreateOrganizationSchema,
  UpdateOrganizationSchema,
  DeleteOrganizationSchema,
} from '@/types/organization';

const organizationProcedure = authedProcedure.use(async (opts) => {
  return opts.next({
    ctx: {
      organizationModel: new OrganizationModel(),
    },
  });
});

export const organizationRouter = router({
  // 根据条件查询组织
  queryOrganizations: organizationProcedure
    .input(OrganizationQuerySchema)
    .query(async ({ ctx, input }) => {
      const result = await ctx.organizationModel.queryOrganizations(input);
      return {
        success: true,
        data: result,
      };
    }),

  // 获取所有组织
  getAllOrganizations: organizationProcedure.query(async ({ ctx }) => {
    const result = await ctx.organizationModel.getAllOrganizations();
    return {
      success: true,
      data: result,
    };
  }),

  // 根据ID获取组织详情
  getOrganization: organizationProcedure
    .input(z.string())
    .query(async ({ ctx, input }) => {
      const result = await ctx.organizationModel.queryOrganization({
        id: input,
      });
      return {
        success: true,
        data: result,
      };
    }),

  // 创建组织
  createOrganization: organizationProcedure
    .input(CreateOrganizationSchema)
    .mutation(async ({ ctx, input }) => {
      const result = await ctx.organizationModel.createOrganization(input);
      return {
        success: true,
        data: result,
      };
    }),

  // 更新组织信息
  updateOrganization: organizationProcedure
    .input(UpdateOrganizationSchema)
    .mutation(async ({ ctx, input }) => {
      const result = await ctx.organizationModel.updateOrganization(
        input.id,
        input.data
      );
      return {
        success: true,
        data: result,
      };
    }),

  // 删除组织
  deleteOrganization: organizationProcedure
    .input(DeleteOrganizationSchema)
    .mutation(async ({ ctx, input }) => {
      const result = await ctx.organizationModel.deleteOrganization(input.id);
      return {
        success: result,
        data: result,
      };
    }),
});
