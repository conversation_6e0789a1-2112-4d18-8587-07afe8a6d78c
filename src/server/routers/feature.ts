import { string, z } from 'zod';

import { authedProcedure, router } from '@/libs/trpc';
import { FeatureModel } from '@/database/models';
import {
  CreateFeatureSchema,
  UpdateFeatureSchema,
  BELONG_TO_OPTIONS,
  FeatureQuerySchema,
} from '@/types/feature';

const featureProcedure = authedProcedure.use(async (opts) => {
  return opts.next({
    ctx: {
      featureModel: new FeatureModel(),
    },
  });
});

export const featureRouter = router({
  // 根据条件查询功能
  queryFeatures: featureProcedure
    .input(FeatureQuerySchema)
    .query(async ({ ctx, input }) => {
      const result = await ctx.featureModel.queryFeatures(input);
      return {
        success: true,
        data: result,
      };
    }),

  // 获取所有功能列表
  getAllFeatures: featureProcedure.query(async ({ ctx }) => {
    const result = await ctx.featureModel.getAllFeatures();
    return {
      success: true,
      data: result,
    };
  }),

  // 获取功能详细
  getFeature: featureProcedure
    .input(z.string())
    .query(async ({ ctx, input }) => {
      const result = await ctx.featureModel.queryFeature({ id: input });
      return {
        success: true,
        data: result,
      };
    }),

  // 创建功能
  createFeature: featureProcedure
    .input(CreateFeatureSchema)
    .mutation(async ({ ctx, input }) => {
      const result = await ctx.featureModel.createFeature(input);
      return {
        success: true,
        data: result,
      };
    }),

  // 更新功能信息
  updateFeature: featureProcedure
    .input(
      z.object({
        id: z.string(),
        data: UpdateFeatureSchema,
      })
    )
    .mutation(async ({ ctx, input }) => {
      const result = await ctx.featureModel.updateFeature(input.id, input.data);
      return {
        success: true,
        data: result,
      };
    }),

  // 删除功能
  deleteFeature: featureProcedure
    .input(z.string())
    .mutation(async ({ ctx, input }) => {
      const result = await ctx.featureModel.deleteFeature(input);

      return {
        success: result,
        data: result,
      };
    }),

  // 检查功能代号是否存在
  isCodeExists: featureProcedure
    .input(
      z.object({
        code: z.string(),
        excludeId: z.string().optional(),
      })
    )
    .query(async ({ ctx, input }) => {
      const result = await ctx.featureModel.isCodeExists(
        input.code,
        input.excludeId
      );
      return {
        success: true,
        data: result,
      };
    }),

  // 批量删除功能
  batchDeleteFeatures: featureProcedure
    .input(z.object({ featureIds: z.array(z.string()) }))
    .mutation(async ({ ctx, input }) => {
      const result = await ctx.featureModel.batchDeleteFeatures(
        input.featureIds
      );
      return {
        success: result,
        data: result,
      };
    }),
});
