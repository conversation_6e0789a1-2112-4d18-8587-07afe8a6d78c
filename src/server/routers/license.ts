import { z } from 'zod';

import { authedProcedure, router } from '@/libs/trpc';
import { LicenseModel } from '@/database/models';
import {
  CreateLicenseSchema,
  UpdateLicenseInputSchema,
  LicenseQuerySchema,
  DeleteLicenseSchema,
} from '@/types/license';

const licenseProcedure = authedProcedure.use(async (opts) => {
  return opts.next({
    ctx: {
      licenseModel: new LicenseModel(),
    },
  });
});

export const licenseRouter = router({
  // 根据条件查询许可证
  queryLicenses: licenseProcedure
    .input(LicenseQuerySchema)
    .query(async ({ ctx, input }) => {
      const result = await ctx.licenseModel.queryLicenses(input);
      return {
        success: true,
        data: result,
      };
    }),

  // 根据ID获取许可证详情
  getLicense: licenseProcedure
    .input(z.string())
    .query(async ({ ctx, input }) => {
      const result = await ctx.licenseModel.queryLicense({
        id: input,
      });
      return {
        success: true,
        data: result,
      };
    }),

  // 创建许可证
  createLicense: licenseProcedure
    .input(CreateLicenseSchema)
    .mutation(async ({ ctx, input }) => {
      const result = await ctx.licenseModel.createLicense(input);
      return {
        success: true,
        data: result,
      };
    }),

  // 更新许可证信息
  updateLicense: licenseProcedure
    .input(UpdateLicenseInputSchema)
    .mutation(async ({ ctx, input }) => {
      const result = await ctx.licenseModel.updateLicense(input.id, input.data);
      return {
        success: true,
        data: result,
      };
    }),

  // 删除许可证
  deleteLicense: licenseProcedure
    .input(DeleteLicenseSchema)
    .mutation(async ({ ctx, input }) => {
      const result = await ctx.licenseModel.deleteLicense(input.id);
      return {
        success: result,
        data: result,
      };
    }),
});
