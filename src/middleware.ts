import { clerkMiddleware, createRouteMatcher } from '@clerk/nextjs/server';
import { NextRequest, NextResponse } from 'next/server';

export const config = {
  matcher: [
    // exclude static files
    '/((?!api|_next/static|_next/image|favicon.ico|favicon-|manifest.webmanifest|sitemap.xml|robots.txt|icons).*)',
    '/',
    '/(api|trpc)(.*)',
  ],
};

const defaultMiddleware = (request: NextRequest) => {
  const url = new URL(request.url);
  if (['/api', '/trpc'].some((path) => url.pathname.startsWith(path)))
    return NextResponse.next();

  // 优先处理 OPTIONS 请求
  if (request.method === 'OPTIONS') {
    return new NextResponse(null, {
      headers: {
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Access-Control-Allow-Methods': 'GET, OPTIONS',
        'Access-Control-Allow-Origin': '*',
      },
    });
  }

  return NextResponse.rewrite(url);
};

const isPublicRoute = createRouteMatcher(['/login(.*)', '/api/v1/license(.*)']);

export default clerkMiddleware(
  async (auth, req) => {
    if (!isPublicRoute(req)) await auth.protect();

    return defaultMiddleware(req);
  },
  {
    // https://github.com/lobehub/lobe-chat/pull/3084
    clockSkewInMs: 120 * 1000,
    signInUrl: '/login',
    signUpUrl: '/login',
  }
);
