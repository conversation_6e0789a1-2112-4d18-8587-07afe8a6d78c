// 统一导出所有类型定义
export * from './common';
export * from './feature';
export * from './license';
export * from './organization';
export * from './user';
export * from './version';

import type {
  PaginatedResponse,
  FilterParams,
  SortParams,
  SearchParams,
} from './common';

// 通用的 API 响应类型
export interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

// 通用的列表查询响应类型
export interface ListResponse<T = unknown> extends PaginatedResponse<T> {
  filters?: FilterParams;
  sort?: SortParams;
  search?: SearchParams;
}
