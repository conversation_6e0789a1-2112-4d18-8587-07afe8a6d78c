import { VersionFeature } from '@/database/schemas';
import { SortOptions, SortSchema } from './common';
import { z } from 'zod';

// 版本查询条件接口
export interface VersionQueryConditions {
  id?: string;
  name?: string;
  isActive?: boolean;
  page?: number;
  pageSize?: number;
  sort?: SortOptions;
}

// Version 版本相关类型定义
export const CreateVersionSchema = z.object({
  name: z.string().min(1, '版本名称不能为空'),
  description: z.string().optional(),
  maxUsers: z.number().min(1, '最大用户数必须大于0').nullish(),
  maxVersion: z.string().nullish(),
  minVersion: z.string().nullish(),
  isActive: z.boolean().default(true),
});

export const UpdateVersionSchema = z.object({
  name: z.string().min(1, '版本名称不能为空').optional(),
  description: z.string().optional(),
  maxUsers: z.number().min(1, '最大用户数必须大于0').nullish(),
  maxVersion: z.string().nullish(),
  minVersion: z.string().nullish(),
  isActive: z.boolean().optional(),
});

// Version Feature 版本功能关联相关类型定义
export const CreateVersionFeatureSchema = z.object({
  versionId: z.string().min(1, '版本ID不能为空'),
  grantFeatures: z.array(z.string()).nullish(),
  revokeFeatures: z.array(z.string()).nullish(),
});

// 查询参数类型定义
export const VersionQuerySchema = z.object({
  id: z.string().optional(),
  name: z.string().optional(),
  isActive: z.boolean().optional(),
  page: z.number().min(1, '页码必须大于0').optional(),
  pageSize: z
    .number()
    .min(1, '每页数量必须大于0')
    .max(100, '每页数量不能超过100')
    .optional(),
  sort: SortSchema,
});

// 常量定义
export const GRANT_TYPES = ['grant', 'revoke'] as const;
export type GrantType = (typeof GRANT_TYPES)[number];

// 业务相关的组合类型
export interface VersionWithFeatures {
  id: string;
  name: string;
  description?: string;
  maxUsers?: number;
  maxVersion?: string;
  minVersion?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  features: Array<VersionFeature>;
}

// 版本权限检查相关类型
export interface VersionPermission {
  versionId: string;
  grantedFeatures: string[]; // 授予的功能ID列表
  revokedFeatures: string[]; // 撤销的功能ID列表
  maxUsers?: number;
  maxVersion?: string;
  minVersion?: string;
  isActive: boolean;
}
