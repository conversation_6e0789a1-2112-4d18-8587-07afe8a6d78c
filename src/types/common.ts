import { z } from 'zod';

// 排序相关类型
export interface SortParams {
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// 统一的排序类型定义，用于 TRPC 接口
export type SortOrder = 'ascend' | 'descend';
export type SortOptions = Record<string, SortOrder> | null;

export const SortSchema = z
  .record(z.string(), z.enum(['ascend', 'descend']))
  .nullish();

// 分页相关类型
export interface PaginationParams {
  page?: number;
  pageSize?: number;
}

export interface PaginatedResponse<T = unknown> {
  success: boolean;
  data: T[];
  pagination: {
    page: number;
    pageSize: number;
    total: number;
    totalPages: number;
  };
}

// 筛选相关类型
export interface FilterParams {
  [key: string]: unknown;
}

// 搜索相关类型
export interface SearchParams {
  keyword?: string;
  fields?: string[];
}

// 完整的查询参数类型
export interface QueryParams
  extends PaginationParams,
    SortParams,
    FilterParams,
    SearchParams {}
