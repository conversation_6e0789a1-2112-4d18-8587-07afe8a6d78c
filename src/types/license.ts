import { z } from 'zod';
import { SortOptions, SortSchema } from './common';
import { Organization, Version } from '@/database/schemas';

// License 查询条件接口
export interface LicenseQueryConditions {
  id?: string;
  organizationId?: string;
  versionId?: string;
  enabled?: boolean;
  page?: number;
  pageSize?: number;
  sort?: SortOptions;
}

// TRPC 路由输入类型定义
export const LicenseQuerySchema = z.object({
  id: z.string().optional(),
  organizationId: z.string().optional(),
  versionId: z.string().optional(),
  enabled: z.boolean().optional(),
  page: z.number().min(1, '页码必须大于0').optional(),
  pageSize: z
    .number()
    .min(1, '每页数量必须大于0')
    .max(100, '每页数量不能超过100')
    .optional(),
  sort: SortSchema,
});

export const CreateLicenseSchema = z
  .object({
    organizationId: z.string().min(1, '组织ID不能为空'),
    versionId: z.string().min(1, '版本ID不能为空'),
    startTime: z.date().min(new Date(), '生效时间不能小于当前时间'),
    endTime: z.date().min(new Date(), '失效时间不能小于当前时间'),
    enabled: z.boolean().default(true),
  })
  .refine((data) => data.endTime > data.startTime, {
    message: '失效时间必须晚于生效时间',
    path: ['endTime'],
  });

export const UpdateLicenseSchema = z
  .object({
    organizationId: z.string().min(1, '组织ID不能为空').optional(),
    versionId: z.string().min(1, '版本ID不能为空').optional(),
    startTime: z.date().optional(),
    endTime: z.date().optional(),
    enabled: z.boolean().optional(),
  })
  .refine(
    (data) => {
      if (data.startTime && data.endTime) {
        return data.endTime > data.startTime;
      }
      return true;
    },
    {
      message: '失效时间必须晚于生效时间',
      path: ['endTime'],
    }
  );

export const UpdateLicenseInputSchema = z.object({
  id: z.string(),
  data: UpdateLicenseSchema,
});

export const DeleteLicenseSchema = z.object({
  id: z.string(),
});

// 导出类型
export type LicenseQueryInput = z.infer<typeof LicenseQuerySchema>;
export type CreateLicenseInput = z.infer<typeof CreateLicenseSchema>;
export type UpdateLicenseInput = z.infer<typeof UpdateLicenseSchema>;
export type UpdateLicenseInputType = z.infer<typeof UpdateLicenseInputSchema>;
export type DeleteLicenseInput = z.infer<typeof DeleteLicenseSchema>;

// 许可证状态枚举
export const LICENSE_STATUS = {
  VALID: 'valid', // 有效（在有效期内且启用）
  EXPIRED: 'expired', // 已过期
  DISABLED: 'disabled', // 已禁用
  PENDING: 'pending', // 待生效（生效时间未到）
} as const;

export type LicenseStatus =
  (typeof LICENSE_STATUS)[keyof typeof LICENSE_STATUS];

// 许可证验证结果类型
export interface LicenseValidation {
  isValid: boolean;
  status: LicenseStatus;
  message?: string;
  expiresIn?: number; // 距离过期的天数
}

// 包含关联信息的许可证类型
export interface LicenseWithRelations {
  id: string;
  organizationId: string;
  versionId: string;
  startTime: Date;
  endTime: Date;
  enabled: boolean;
  createdAt: Date;
  updatedAt: Date;
  organization: Organization;
  version: Version;
  [key: string]: unknown;
}
