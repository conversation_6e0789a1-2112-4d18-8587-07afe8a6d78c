import { z } from 'zod';
import { SortOptions, SortSchema } from './common';

export interface FeatureQueryConditions {
  id?: string;
  code?: string;
  category?: string;
  belongTo?: 'lobe' | 'admin';
  name?: string;
  page?: number;
  pageSize?: number;
  sort?: SortOptions;
}

// Feature 相关的输入类型定义
export const CreateFeatureSchema = z.object({
  name: z.string().min(1, '功能名称不能为空'),
  code: z.string().min(1, '功能代号不能为空'),
  category: z.string().nullish(),
  description: z.string().nullish(),
  belongTo: z.enum(['lobe', 'admin']).optional(),
});

export const UpdateFeatureSchema = z.object({
  name: z.string().min(1, '功能名称不能为空').optional(),
  code: z.string().min(1, '功能代号不能为空').optional(),
  category: z.string().nullish(),
  description: z.string().nullish(),
  belongTo: z.enum(['lobe', 'admin']).optional(),
});

export const FeatureQuerySchema = z.object({
  id: z.string().optional(),
  code: z.string().optional(),
  category: z.string().optional(),
  belongTo: z.enum(['lobe', 'admin']).optional(),
  name: z.string().optional(),
  page: z.number().min(1, '页码必须大于0').optional(),
  pageSize: z
    .number()
    .min(1, '每页数量必须大于0')
    .max(100, '每页数量不能超过100')
    .optional(),
  sort: SortSchema,
});

// TypeScript 类型导出
export type CreateFeatureInput = z.infer<typeof CreateFeatureSchema>;
export type UpdateFeatureInput = z.infer<typeof UpdateFeatureSchema>;
export type FeatureQueryInput = z.infer<typeof FeatureQuerySchema>;

// Feature 分类常量
export const FEATURE_CATEGORIES = [
  'chat',
  'admin',
  'model',
  'plugin',
  'analytics',
] as const;

export type FeatureCategory = (typeof FEATURE_CATEGORIES)[number];

// 所属系统常量
export const BELONG_TO_OPTIONS = ['lobe', 'admin'] as const;
export type BelongToOption = (typeof BELONG_TO_OPTIONS)[number];
