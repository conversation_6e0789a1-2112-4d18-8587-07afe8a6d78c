import { z } from 'zod';
import { SortOptions, SortSchema } from './common';

// 联系人信息类型
export interface ContactPerson {
  name: string;
  title?: string;
  email?: string;
  phone?: string;
  wechat?: string;
}

// 组织查询条件接口
export interface OrganizationQueryConditions {
  id?: string;
  chineseName?: string;
  englishName?: string;
  country?: string;
  page?: number;
  pageSize?: number;
  sort?: SortOptions;
}

// TRPC 路由输入类型定义
export const OrganizationQuerySchema = z.object({
  id: z.string().optional(),
  chineseName: z.string().optional(),
  englishName: z.string().optional(),
  country: z.string().optional(),
  page: z.number().min(1, '页码必须大于0').optional(),
  pageSize: z.number().optional(),
  sort: SortSchema,
});

export const CreateOrganizationSchema = z.object({
  chineseName: z.string(),
  englishName: z.string(),
  logo: z.string().nullish(),
  scale: z.string().nullish(),
  industry: z.string().nullish(),
  country: z.string().nullish(),
  description: z.string().nullish(),
  assigner: z.any().nullish(),
  lobeOwner: z.any().nullish(),
});

export const UpdateOrganizationSchema = z.object({
  id: z.string(),
  data: z.object({
    chineseName: z.string(),
    englishName: z.string(),
    logo: z.string().nullish(),
    scale: z.string().nullish(),
    industry: z.string().nullish(),
    country: z.string().nullish(),
    description: z.string().nullish(),
    assigner: z.any().nullish(),
    lobeOwner: z.any().nullish(),
  }),
});

export const DeleteOrganizationSchema = z.object({
  id: z.string(),
});

// 导出类型
export type OrganizationQueryInput = z.infer<typeof OrganizationQuerySchema>;
export type CreateOrganizationInput = z.infer<typeof CreateOrganizationSchema>;
export type UpdateOrganizationInput = z.infer<typeof UpdateOrganizationSchema>;
export type DeleteOrganizationInput = z.infer<typeof DeleteOrganizationSchema>;
