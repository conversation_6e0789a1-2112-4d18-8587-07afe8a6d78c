import { subscribeWithSelector } from 'zustand/middleware';
import { shallow } from 'zustand/shallow';
import { createWithEqualityFn } from 'zustand/traditional';
import { StateCreator } from 'zustand/vanilla';

import { createDevtools } from '../middleware/createDevtools';
import { type UserAuthAction, createAuthSlice } from './action';
import { type UserAuthState, initialAuthState } from './initialState';

//  ===============  聚合 createStoreFn ============ //

export type UserStore = UserAuthState & UserAuthAction;

const createStore: StateCreator<UserStore, [['zustand/devtools', never]]> = (
  ...parameters
) => ({
  ...initialAuthState,
  ...createAuthSlice(...parameters),
});

//  ===============  实装 useStore ============ //

const devtools = createDevtools('user');

export const useUserStore = createWithEqualityFn<UserStore>()(
  subscribeWithSelector(devtools(createStore)),
  shallow
);
