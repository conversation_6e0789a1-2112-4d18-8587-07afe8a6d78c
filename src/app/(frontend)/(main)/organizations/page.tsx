'use client';

import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>confirm, Space, Tag, Tooltip } from 'antd';
import { ActionType, ProColumns } from '@ant-design/pro-components';
import { ActionIcon } from '@lobehub/ui';
import { Edit3Icon, EyeIcon, PlusIcon, Trash } from 'lucide-react';
import { ReactNode, useRef, useState } from 'react';

import Table from '@/components/Table';
import { trpcClient } from '@/libs/trpc/client';
import { Organization } from '@/database/schemas';
import { OrganizationFormModal, OrganizationDetailModal } from './features';

const OrganizationManagementPage = () => {
  const { message } = App.useApp();

  const actionRef = useRef<ActionType>(null);

  const [organizationId, setOrganizationId] = useState<string | undefined>();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [detailOrganizationId, setDetailOrganizationId] = useState<
    string | undefined
  >();
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);

  // 删除组织
  const handleDeleteOrganization = async (id: string) => {
    const { success } = await trpcClient.organization.deleteOrganization.mutate(
      { id }
    );

    if (success) {
      message.success('删除成功');
      actionRef.current?.reload();
    } else {
      message.error('删除失败');
    }
  };

  // 打开弹窗
  const handleOpenModal = (organizationId?: string) => {
    setOrganizationId(organizationId);
    setIsModalOpen(true);
  };

  // 关闭弹窗
  const handleCloseModal = () => {
    setOrganizationId(undefined);
    setIsModalOpen(false);
  };

  // 成功回调
  const handleSuccess = () => {
    handleCloseModal();
    actionRef.current?.reload();
  };

  // 打开详情弹窗
  const handleOpenDetailModal = (organizationId: string) => {
    setDetailOrganizationId(organizationId);
    setIsDetailModalOpen(true);
  };

  // 关闭详情弹窗
  const handleCloseDetailModal = () => {
    setDetailOrganizationId(undefined);
    setIsDetailModalOpen(false);
  };

  // 表格列定义
  const columns: ProColumns<Organization>[] = [
    {
      title: '中文名称',
      dataIndex: 'chineseName',
      ellipsis: true,
      copyable: true,
      width: 200,
    },
    {
      title: '英文名称',
      dataIndex: 'englishName',
      width: 200,
      ellipsis: true,
      copyable: true,
    },
    {
      title: '国家/地区',
      dataIndex: 'country',
      width: 150,
      ellipsis: true,
    },
    {
      title: '组织描述',
      dataIndex: 'description',
      ellipsis: true,
      search: false,
      hideInSearch: true,
      width: 200,
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      width: 180,
      search: false,
      sorter: true,
      valueType: 'dateTime',
    },
    {
      title: '操作',
      valueType: 'option',
      width: 150,
      fixed: 'right',
      render: (_, record) => (
        <Space size='small'>
          <Tooltip title='查看详情'>
            <ActionIcon
              icon={EyeIcon}
              size='small'
              onClick={() => handleOpenDetailModal(record.id)}
            />
          </Tooltip>
          <ActionIcon
            icon={Edit3Icon}
            size='small'
            onClick={() => handleOpenModal(record.id)}
          />
          <Popconfirm
            title='确认删除'
            description='确定要删除该组织吗？'
            onConfirm={() => handleDeleteOrganization(record.id)}
            okText='确认'
            cancelText='取消'
          >
            <Tooltip title='删除'>
              <ActionIcon
                icon={Trash}
                size='small'
                style={{ color: '#ff4d4f' }}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <>
      <Table<Organization>
        columns={columns}
        rowKey='id'
        actionRef={actionRef}
        toolBarRender={() => [
          <Button
            key='add'
            type='primary'
            icon={<PlusIcon size={16} />}
            onClick={() => handleOpenModal()}
          >
            添加组织
          </Button>,
        ]}
        options={false}
        request={async (params, sort) => {
          const { data } =
            await trpcClient.organization.queryOrganizations.query({
              ...params,
              sort: sort as Record<string, 'ascend' | 'descend'>,
              page: params.current,
              pageSize: params.pageSize,
            });

          return {
            data: data.data,
            total: data.total,
            success: true,
          };
        }}
        scroll={{ x: 1400 }}
      />
      <OrganizationFormModal
        open={isModalOpen}
        organizationId={organizationId}
        onCancel={handleCloseModal}
        onSuccess={handleSuccess}
      />
      <OrganizationDetailModal
        open={isDetailModalOpen}
        organizationId={detailOrganizationId}
        onCancel={handleCloseDetailModal}
      />
    </>
  );
};

export default OrganizationManagementPage;
