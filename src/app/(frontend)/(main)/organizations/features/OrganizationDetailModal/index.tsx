'use client';

import { Modal, Flex, Image } from 'antd';
import { ProDescriptions } from '@ant-design/pro-components';
import type { ProDescriptionsItemProps } from '@ant-design/pro-components';
import { trpcQuery } from '@/libs/trpc/client';
import { Organization } from '@/database/schemas';
import { ContactPerson } from '@/types';
import { ReactNode, useEffect } from 'react';

interface OrganizationDetailModalProps {
  open: boolean;
  organizationId?: string;
  onCancel: () => void;
}

const OrganizationDetailModal: React.FC<OrganizationDetailModalProps> = ({
  open,
  organizationId,
  onCancel,
}) => {
  const { isFetching, data } = trpcQuery.organization.getOrganization.useQuery(
    organizationId || '',
    {
      enabled: !!organizationId,
    }
  );

  // 定义 ProDescriptions 的 columns
  const columns: ProDescriptionsItemProps<Partial<Organization>>[] = [
    {
      title: '组织ID',
      dataIndex: 'id',
      copyable: true,
    },
    {
      title: '中文名称',
      dataIndex: 'chineseName',
      copyable: true,
      ellipsis: true,
    },
    {
      title: '英文名称',
      dataIndex: 'englishName',
      copyable: true,
      ellipsis: true,
    },
    {
      title: 'Logo',
      dataIndex: 'logo',
      ellipsis: true,
      copyable: true,
      render: (_: ReactNode, record) => {
        return record.logo ? (
          <Image
            alt={record.chineseName}
            src={record.logo as string}
            width={100}
            height={100}
          />
        ) : null;
      },
    },
    {
      title: '组织规模',
      dataIndex: 'scale',
      ellipsis: true,
    },
    {
      title: '所属行业',
      dataIndex: 'industry',
      ellipsis: true,
    },
    {
      title: '国家/地区',
      dataIndex: 'country',
      ellipsis: true,
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      valueType: 'dateTime',
    },
    {
      title: '更新时间',
      dataIndex: 'updatedAt',
      valueType: 'dateTime',
    },
    {
      title: '组织描述',
      dataIndex: 'description',
      ellipsis: true,
    },
  ];

  const contractUserColumns: ProDescriptionsItemProps<ContactPerson>[] = [
    {
      title: '姓名',
      dataIndex: 'name',
      span: 2,
      ellipsis: true,
    },
    {
      title: '邮箱',
      dataIndex: 'email',
      span: 2,
      ellipsis: true,
    },
    {
      title: '电话',
      dataIndex: 'phone',
      span: 2,
      ellipsis: true,
    },
    {
      title: '微信',
      dataIndex: 'wechat',
      span: 2,
      ellipsis: true,
    },
  ];

  return (
    <Modal
      title='组织详情'
      open={open}
      onCancel={onCancel}
      footer={null}
      width={800}
    >
      <Flex vertical gap={16}>
        <ProDescriptions
          columns={columns}
          dataSource={data?.data as Organization}
          loading={isFetching}
        />
        <ProDescriptions
          title='组织对接人'
          columns={contractUserColumns}
          dataSource={data?.data?.assigner as ContactPerson}
          loading={isFetching}
        />
        <ProDescriptions
          title='Lobe负责人'
          columns={contractUserColumns}
          dataSource={data?.data?.lobeOwner as ContactPerson}
          loading={isFetching}
        />
      </Flex>
    </Modal>
  );
};

export default OrganizationDetailModal;
