import { ProFormText } from '@ant-design/pro-components';

interface ContractUserProps {
  prefix: string;
}

const ContractUser = ({ prefix }: ContractUserProps) => {
  return (
    <>
      <ProFormText name={[prefix, 'name']} label='姓名' />
      <ProFormText name={[prefix, 'email']} label='邮箱' />
      <ProFormText name={[prefix, 'phone']} label='电话' />
      <ProFormText name={[prefix, 'wechat']} label='微信' />
    </>
  );
};

export default ContractUser;
