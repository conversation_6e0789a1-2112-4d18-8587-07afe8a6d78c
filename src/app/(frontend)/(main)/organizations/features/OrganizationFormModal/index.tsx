'use client';

import { App, Form, Divider, Drawer, <PERSON><PERSON>, Button } from 'antd';
import {
  ProForm,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { trpcClient } from '@/libs/trpc/client';
import { useState } from 'react';
import ContractUser from './ContractUser';

interface OrganizationFormModalProps {
  open: boolean;
  organizationId?: string;
  onCancel: () => void;
  onSuccess: () => void;
}

const OrganizationFormModal: React.FC<OrganizationFormModalProps> = ({
  open,
  onCancel,
  onSuccess,
  organizationId,
}) => {
  const { message } = App.useApp();

  const [form] = Form.useForm();

  const [isLoading, setIsLoading] = useState(false);

  // 提交表单
  const handleSubmit = async () => {
    setIsLoading(true);

    try {
      const values = await form.validateFields();

      let result;

      if (organizationId) {
        result = await trpcClient.organization.updateOrganization.mutate({
          id: organizationId,
          data: values,
        });
      } else {
        result =
          await trpcClient.organization.createOrganization.mutate(values);
      }

      if (result.success) {
        message.success(organizationId ? '编辑成功' : '添加成功');
        onSuccess();
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Drawer
      destroyOnHidden
      title={organizationId ? '编辑组织' : '添加组织'}
      open={open}
      onClose={onCancel}
      width={700}
      footer={
        <Flex justify='space-between' align='center' style={{ marginTop: 16 }}>
          <Button onClick={onCancel}>取消</Button>
          <Button type='primary' loading={isLoading} onClick={handleSubmit}>
            提交
          </Button>
        </Flex>
      }
    >
      <ProForm
        submitter={false}
        form={form}
        layout='vertical'
        request={async () => {
          if (!organizationId) {
            // 如果 organizationId 为空，则重置表单
            setTimeout(form.resetFields);
            return {};
          }

          const { data } =
            await trpcClient.organization.getOrganization.query(organizationId);

          setTimeout(() => {
            form.setFieldsValue(data);
          });

          return data;
        }}
      >
        <Divider orientation='left'>基本信息</Divider>

        <ProFormText
          name='chineseName'
          label='中文名称'
          rules={[
            { required: true, message: '请输入组织中文名称' },
            { max: 100, message: '组织中文名称不能超过100个字符' },
          ]}
          placeholder='请输入组织中文名称'
        />

        <ProFormText
          name='englishName'
          label='英文名称'
          rules={[
            { required: true, message: '请输入组织英文名称' },
            { max: 100, message: '组织英文名称不能超过100个字符' },
          ]}
          placeholder='请输入组织英文名称'
        />

        <ProFormText
          name='logo'
          label='Logo URL'
          rules={[{ type: 'url', message: '请输入正确的URL格式' }]}
          placeholder='请输入组织Logo的URL地址'
        />

        <Divider orientation='left'>组织信息</Divider>

        <ProFormText name='scale' label='组织规模' />

        <ProFormText name='industry' label='所属行业' />

        <ProFormText name='country' label='国家/地区' />

        <ProFormTextArea
          name='description'
          label='组织描述'
          rules={[{ max: 500, message: '组织描述不能超过500个字符' }]}
          placeholder='请输入组织描述信息'
          fieldProps={{
            rows: 4,
          }}
        />

        <Divider orientation='left'>组织对接人</Divider>
        <ContractUser prefix='assigner' />

        <Divider orientation='left'>Lobe负责人</Divider>
        <ContractUser prefix='lobeOwner' />
      </ProForm>
    </Drawer>
  );
};

export default OrganizationFormModal;
