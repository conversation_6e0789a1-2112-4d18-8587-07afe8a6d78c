'use client';

import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>firm, Space, Switch } from 'antd';
import { ActionType, ProColumns } from '@ant-design/pro-components';
import { ActionIcon } from '@lobehub/ui';
import { Edit3Icon, PlusIcon, Trash } from 'lucide-react';
import { Key, useRef, useState } from 'react';
import dayjs from 'dayjs';

import Table from '@/components/Table';
import { trpcClient } from '@/libs/trpc/client';
import { LicenseFormModal } from './features';
import { LicenseWithRelations } from '@/types';
import { useOrganizationOptions, useVersionOptions } from '@/hooks';

const LicenseManagementPage = () => {
  const { message } = App.useApp();

  const actionRef = useRef<ActionType>(null);

  const [licenseId, setLicenseId] = useState<string | undefined>();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<Key[]>([]);

  const organizationOptions = useOrganizationOptions();
  const versionOptions = useVersionOptions();

  // 切换启用状态
  const handleToggleEnabled = async (id: string, enabled: boolean) => {
    const { success } = await trpcClient.license.updateLicense.mutate({
      id,
      data: { enabled },
    });

    if (success) {
      message.success(enabled ? '许可证已启用' : '许可证已禁用');
      actionRef.current?.reload();
    } else {
      message.error('状态更新失败');
    }
  };

  // 删除许可证
  const handleDeleteLicense = async (id: string) => {
    const { success } = await trpcClient.license.deleteLicense.mutate({ id });

    if (success) {
      message.success('删除成功');
      actionRef.current?.reload();
    } else {
      message.error('删除失败');
    }
  };

  // 批量删除许可证
  const handleBatchDelete = async (ids: string[]) => {
    // 由于当前 API 中没有批量删除，我们逐个删除
    try {
      const promises = ids.map((id) =>
        trpcClient.license.deleteLicense.mutate({ id })
      );

      const results = await Promise.all(promises);
      const successCount = results.filter((result) => result.success).length;

      if (successCount === ids.length) {
        message.success(`批量删除成功，共删除 ${successCount} 个许可证`);
      } else {
        message.warning(
          `部分删除成功，成功删除 ${successCount}/${ids.length} 个许可证`
        );
      }

      actionRef.current?.reload();
      setSelectedRowKeys([]);
    } catch (error) {
      message.error('批量删除失败');
    }
  };

  // 打开弹窗
  const handleOpenModal = (licenseId?: string) => {
    setLicenseId(licenseId);
    setIsModalOpen(true);
  };

  // 关闭弹窗
  const handleCloseModal = () => {
    setLicenseId(undefined);
    setIsModalOpen(false);
  };

  // 成功回调
  const handleSuccess = () => {
    handleCloseModal();
    actionRef.current?.reload();
  };

  // 表格列定义
  const columns: ProColumns<LicenseWithRelations>[] = [
    {
      title: 'License ID',
      dataIndex: 'id',
      width: 200,
      hideInSearch: true,
      ellipsis: true,
      copyable: true,
    },
    {
      title: '组织名称',
      dataIndex: 'organization',
      width: 200,
      ellipsis: true,
      renderText: (_, record) => {
        const { chineseName, englishName } = record.organization;

        return `${chineseName} (${englishName})`;
      },
      valueType: 'select',
      fieldProps: {
        allowClear: true,
        options: organizationOptions,
      },
    },
    {
      title: '版本',
      dataIndex: ['version', 'name'],
      width: 150,
      renderText: (_, record) => record.version?.name,
      valueType: 'select',
      fieldProps: {
        allowClear: true,
        options: versionOptions,
      },
    },
    {
      title: '有效期',
      dataIndex: 'startTime',
      width: 300,
      search: false,
      render: (_, record) => (
        <span>
          {dayjs(record.startTime).format('YYYY-MM-DD HH:mm')} ~{' '}
          {dayjs(record.endTime).format('YYYY-MM-DD HH:mm')}
        </span>
      ),
    },
    {
      title: '启用状态',
      dataIndex: 'enabled',
      width: 100,
      valueType: 'select',
      fieldProps: {
        options: [
          { label: '启用', value: true },
          { label: '禁用', value: false },
        ],
      },
      render: (_, record) => (
        <Switch
          checked={record.enabled}
          onChange={(checked) => handleToggleEnabled(record.id, checked)}
        />
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      width: 180,
      hideInSearch: true,
      sorter: true,
      valueType: 'dateTime',
    },
    {
      title: '操作',
      valueType: 'option',
      width: 120,
      fixed: 'right',
      render: (_, record) => (
        <Space size='small'>
          <ActionIcon
            icon={Edit3Icon}
            size='small'
            onClick={() => handleOpenModal(record.id)}
          />
          <Popconfirm
            title='确认删除'
            description={`确定要删除该许可证吗？`}
            onConfirm={() => handleDeleteLicense(record.id)}
            okText='确认'
            cancelText='取消'
          >
            <ActionIcon
              icon={Trash}
              size='small'
              style={{ color: '#ff4d4f' }}
            />
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <>
      <Table<LicenseWithRelations>
        columns={columns}
        rowKey='id'
        actionRef={actionRef}
        toolBarRender={() => [
          selectedRowKeys.length > 0 && (
            <Popconfirm
              key='delete'
              title='确认删除'
              description={`确定要删除 ${selectedRowKeys.length} 个许可证吗？`}
              onConfirm={() => handleBatchDelete(selectedRowKeys as string[])}
              okText='确认'
              cancelText='取消'
              placement='topRight'
            >
              <Button danger type='primary' icon={<Trash size={16} />}>
                批量删除
              </Button>
            </Popconfirm>
          ),
          <Button
            key='add'
            type='primary'
            icon={<PlusIcon size={16} />}
            onClick={() => handleOpenModal()}
          >
            添加许可证
          </Button>,
        ]}
        options={false}
        rowSelection={{
          selectedRowKeys,
          onChange: setSelectedRowKeys,
        }}
        request={async (params, sort) => {
          const { data } = await trpcClient.license.queryLicenses.query({
            ...params,
            sort,
            page: params.current,
            pageSize: params.pageSize,
          });

          return {
            data: data.data,
            total: data.total,
            success: true,
          };
        }}
      />
      <LicenseFormModal
        open={isModalOpen}
        licenseId={licenseId}
        onCancel={handleCloseModal}
        onSuccess={handleSuccess}
        organizationOptions={organizationOptions}
        versionOptions={versionOptions}
      />
    </>
  );
};

export default LicenseManagementPage;
