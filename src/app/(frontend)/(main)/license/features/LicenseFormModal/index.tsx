'use client';

import { App, Form, Modal } from 'antd';
import {
  ProForm,
  ProFormSelect,
  ProFormDatePicker,
} from '@ant-design/pro-components';
import { trpcClient } from '@/libs/trpc/client';
import { useState } from 'react';
import dayjs from 'dayjs';
import { DefaultOptionType } from 'antd/es/select';

interface LicenseFormModalProps {
  open: boolean;
  licenseId?: string;
  onCancel: () => void;
  onSuccess: () => void;
  organizationOptions?: DefaultOptionType[];
  versionOptions?: DefaultOptionType[];
}

const LicenseFormModal: React.FC<LicenseFormModalProps> = ({
  open,
  onCancel,
  onSuccess,
  licenseId,
  organizationOptions = [],
  versionOptions = [],
}) => {
  const { message } = App.useApp();

  const [form] = Form.useForm();

  const [isLoading, setIsLoading] = useState(false);

  // 提交表单
  const handleSubmit = async () => {
    setIsLoading(true);

    try {
      const values = await form.validateFields();

      // 转换日期格式
      const formData = {
        ...values,
        startTime: dayjs(values.startTime).toDate(),
        endTime: dayjs(values.endTime).toDate(),
      };

      let result;

      if (licenseId) {
        result = await trpcClient.license.updateLicense.mutate({
          id: licenseId,
          data: formData,
        });
      } else {
        result = await trpcClient.license.createLicense.mutate(formData);
      }

      if (result.success) {
        message.success(licenseId ? '编辑成功' : '添加成功');
        onSuccess();
      } else {
        message.error(licenseId ? '编辑失败' : '添加失败');
      }
    } catch (error) {
      console.error('表单提交错误:', error);
      message.error('操作失败，请检查输入信息');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Modal
      destroyOnHidden
      title={licenseId ? '编辑许可证' : '添加许可证'}
      open={open}
      onOk={handleSubmit}
      onCancel={onCancel}
      width={600}
      confirmLoading={isLoading}
    >
      <ProForm
        grid
        submitter={false}
        form={form}
        request={async () => {
          if (!licenseId) {
            // 如果 licenseId 为空，则重置表单并设置默认值
            setTimeout(() => {
              form.resetFields();
              form.setFieldsValue({
                enabled: true, // 默认启用
              });
            });
            return { enabled: true };
          }

          const { data } = await trpcClient.license.getLicense.query(licenseId);

          if (data) {
            // 转换日期格式为 dayjs 对象
            return {
              ...data,
              startTime: dayjs(data.startTime),
              endTime: dayjs(data.endTime),
            };
          }

          return {};
        }}
      >
        <ProFormSelect
          disabled={!!licenseId}
          name='organizationId'
          label='选择组织'
          rules={[{ required: true, message: '请选择组织' }]}
          fieldProps={{
            showSearch: true,
            optionFilterProp: 'label',
            options: organizationOptions,
          }}
          colProps={{
            span: 24,
          }}
        />

        <ProFormSelect
          name='versionId'
          label='选择版本'
          rules={[{ required: true, message: '请选择版本' }]}
          fieldProps={{
            showSearch: true,
            optionFilterProp: 'label',
            options: versionOptions,
          }}
          colProps={{
            span: 24,
          }}
        />

        <ProFormDatePicker
          name='startTime'
          label='开始时间'
          rules={[{ required: true, message: '请选择开始时间' }]}
          placeholder='请选择开始时间'
          fieldProps={{
            minDate: dayjs(),
            style: {
              width: '100%',
            },
          }}
          colProps={{
            span: 12,
          }}
        />

        <ProFormDatePicker
          name='endTime'
          label='结束时间'
          rules={[
            { required: true, message: '请选择结束时间' },
            {
              validator: (_: unknown, value: dayjs.Dayjs) => {
                if (!value || !form.getFieldValue('startTime')) {
                  return Promise.resolve();
                }

                const startTime = form.getFieldValue('startTime');

                if (dayjs(value).isAfter(dayjs(startTime))) {
                  return Promise.resolve();
                }

                return Promise.reject(new Error('结束时间必须晚于开始时间'));
              },
            },
          ]}
          placeholder='请选择结束时间'
          fieldProps={{
            minDate: dayjs(),
            style: {
              width: '100%',
            },
          }}
          colProps={{
            span: 12,
          }}
        />
      </ProForm>
    </Modal>
  );
};

export default LicenseFormModal;
