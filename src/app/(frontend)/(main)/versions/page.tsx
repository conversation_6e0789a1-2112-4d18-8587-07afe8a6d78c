'use client';

import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>firm, Space, Switch } from 'antd';
import { ActionType, ProColumns } from '@ant-design/pro-components';
import { ActionIcon } from '@lobehub/ui';
import { Edit3Icon, Link, PlusIcon, Trash } from 'lucide-react';
import { Key, ReactNode, useRef, useState } from 'react';

import Table from '@/components/Table';
import { trpcClient } from '@/libs/trpc/client';
import { Version } from '@/database/schemas';
import { VersionFeaturesModal, VersionFormModal } from './versions';

const VersionManagementPage = () => {
  const { message } = App.useApp();

  const actionRef = useRef<ActionType>(null);

  const [selectedRowKeys, setSelectedRowKeys] = useState<Key[]>([]);

  const [versionId, setVersionId] = useState<string | undefined>();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isFeaturesModalOpen, setIsFeaturesModalOpen] = useState(false);

  // 删除版本
  const handleDeleteVersion = async (id: string) => {
    const { success } = await trpcClient.version.deleteVersion.mutate(id);

    if (success) {
      message.success('删除成功');
      actionRef.current?.reload();
    } else {
      message.error('删除失败');
    }
  };

  // 批量删除版本
  const handleBatchDelete = async (ids: string[]) => {
    const { success } = await trpcClient.version.batchDeleteVersions.mutate({
      versionIds: ids,
    });

    if (success) {
      message.success('批量删除成功');
      actionRef.current?.reload();
    } else {
      message.error('批量删除失败');
    }
  };

  // 切换版本状态
  const handleToggleStatus = async (versionId: string, isActive: boolean) => {
    try {
      const { success } = await trpcClient.version.updateVersion.mutate({
        id: versionId,
        data: { isActive },
      });

      if (success) {
        message.success(`版本已${isActive ? '启用' : '禁用'}`);
        actionRef.current?.reload();
      } else {
        message.error('状态更新失败');
      }
    } catch (error) {
      message.error('状态更新失败');
    }
  };

  // 打开创建/编辑版本弹窗
  const handleOpenModal = (versionId?: string) => {
    setVersionId(versionId);
    setIsModalOpen(true);
  };

  // 关闭创建/编辑版本弹窗
  const handleCloseModal = () => {
    setVersionId(undefined);
    setIsModalOpen(false);
  };

  // 创建/编辑版本成功回调
  const handleSuccess = () => {
    handleCloseModal();
    actionRef.current?.reload();
  };

  // 打开关联功能弹窗
  const handleOpenFeaturesModal = (versionId: string) => {
    setVersionId(versionId);
    setIsFeaturesModalOpen(true);
  };

  // 关闭关联功能弹窗
  const handleCloseFeaturesModal = () => {
    setVersionId(undefined);
    setIsFeaturesModalOpen(false);
  };

  // 表格列定义
  const columns: ProColumns<Version>[] = [
    {
      title: '版本名称',
      dataIndex: 'name',
      ellipsis: true,
      copyable: true,
      width: 200,
    },
    {
      title: '版本描述',
      dataIndex: 'description',
      ellipsis: true,
      search: false,
      hideInSearch: true,
      width: 300,
    },
    {
      title: '最大用户数',
      dataIndex: 'maxUsers',
      width: 120,
      hideInSearch: true,
      renderText: (value: number) => value || '无限制',
    },
    {
      title: '版本范围',
      dataIndex: 'version_range',
      width: 200,
      hideInSearch: true,
      search: false,
      renderText: (_: ReactNode, record: Version) => {
        const { minVersion, maxVersion } = record;

        if (!minVersion && !maxVersion) {
          return '不限';
        }

        if (minVersion && !maxVersion) {
          return `>= ${minVersion}`;
        }

        if (!minVersion && maxVersion) {
          return `<= ${maxVersion}`;
        }

        return `${minVersion} - ${maxVersion}`;
      },
    },
    {
      title: '状态',
      dataIndex: 'isActive',
      width: 100,
      valueType: 'select',
      fieldProps: {
        options: [
          { label: '启用', value: true },
          { label: '禁用', value: false },
        ],
      },
      render: (_, record) => (
        <Switch
          checked={record.isActive}
          onChange={(checked) => handleToggleStatus(record.id, checked)}
        />
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      width: 180,
      search: false,
      sorter: true,
      valueType: 'dateTime',
    },
    {
      title: '操作',
      valueType: 'option',
      width: 120,
      fixed: 'right',
      render: (_, record) => (
        <Space size='small'>
          <ActionIcon
            icon={Edit3Icon}
            size='small'
            onClick={() => handleOpenModal(record.id)}
          />
          <ActionIcon
            icon={Link}
            size='small'
            onClick={() => handleOpenFeaturesModal(record.id)}
          />
          <Popconfirm
            title='确认删除'
            description='确定要删除该版本吗？'
            onConfirm={() => handleDeleteVersion(record.id)}
            okText='确认'
            cancelText='取消'
          >
            <ActionIcon
              icon={Trash}
              size='small'
              style={{ color: '#ff4d4f' }}
            />
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <>
      <Table<Version>
        columns={columns}
        rowKey='id'
        actionRef={actionRef}
        toolBarRender={() => [
          selectedRowKeys.length > 0 && (
            <Popconfirm
              key='delete'
              title='确认删除'
              description={`确定要删除 ${selectedRowKeys.length} 个版本吗？`}
              onConfirm={() => handleBatchDelete(selectedRowKeys as string[])}
              okText='确认'
              cancelText='取消'
              placement='topRight'
            >
              <Button danger type='primary' icon={<Trash size={16} />}>
                批量删除
              </Button>
            </Popconfirm>
          ),
          <Button
            key='add'
            type='primary'
            icon={<PlusIcon size={16} />}
            onClick={() => handleOpenModal()}
          >
            添加版本
          </Button>,
        ]}
        options={false}
        rowSelection={{
          selectedRowKeys,
          onChange: setSelectedRowKeys,
        }}
        request={async (params, sort) => {
          const { data } = await trpcClient.version.queryVersions.query({
            ...params,
            sort,
            page: params.current,
            pageSize: params.pageSize,
          });

          return {
            data: data.data,
            total: data.total,
            success: true,
          };
        }}
      />
      <VersionFormModal
        open={isModalOpen}
        versionId={versionId}
        onCancel={handleCloseModal}
        onSuccess={handleSuccess}
      />
      <VersionFeaturesModal
        open={isFeaturesModalOpen}
        versionId={versionId ?? ''}
        onCancel={handleCloseFeaturesModal}
        onSuccess={handleCloseFeaturesModal}
      />
    </>
  );
};

export default VersionManagementPage;
