'use client';

import { App, Form, Modal } from 'antd';
import {
  ProForm,
  ProFormDigit,
  ProFormSelect,
  ProFormSwitch,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { trpcClient } from '@/libs/trpc/client';
import { useState } from 'react';

interface VersionFormModalProps {
  open: boolean;
  versionId?: string;
  onCancel: () => void;
  onSuccess: () => void;
}

const VersionFormModal: React.FC<VersionFormModalProps> = ({
  open,
  onCancel,
  onSuccess,
  versionId,
}) => {
  const { message } = App.useApp();

  const [form] = Form.useForm();

  const [isLoading, setIsLoading] = useState(false);

  // 提交表单
  const handleSubmit = async () => {
    setIsLoading(true);

    try {
      const values = await form.validateFields();

      let success;

      if (versionId) {
        const result = await trpcClient.version.updateVersion.mutate({
          id: versionId,
          data: values,
        });
        success = result.success;
      } else {
        const result = await trpcClient.version.createVersion.mutate(values);
        success = result.success;
      }

      if (success) {
        message.success(versionId ? '编辑成功' : '添加成功');
        onSuccess();
      } else {
        message.error(versionId ? '编辑失败' : '添加失败');
      }
    } catch (error) {
      console.error('版本操作失败:', error);
      message.error('操作失败，请稍后重试');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Modal
      destroyOnHidden
      title={versionId ? '编辑版本' : '添加版本'}
      open={open}
      onOk={handleSubmit}
      onCancel={onCancel}
      width={600}
      confirmLoading={isLoading}
    >
      <ProForm
        submitter={false}
        form={form}
        request={async () => {
          if (!versionId) {
            // 如果 versionId 为空，则重置表单
            setTimeout(form.resetFields);
            return {};
          }

          const { data } = await trpcClient.version.getVersion.query(versionId);

          setTimeout(() => {
            form.setFieldsValue(data);
          });

          return data;
        }}
      >
        <ProFormText
          name='name'
          label='版本名称'
          rules={[{ required: true }]}
        />

        <ProFormTextArea
          name='description'
          label='版本描述'
          rules={[{ max: 500 }]}
        />

        <ProFormDigit
          name='maxUsers'
          label='最大用户数'
          fieldProps={{ min: 1, precision: 0 }}
          placeholder='不填写表示无限制'
        />

        <ProFormText
          name='minVersion'
          label='最低版本号'
          placeholder='如: 1.0.0, 不填写表示无限制'
        />

        <ProFormText
          name='maxVersion'
          label='最高版本号'
          placeholder='如: 2.0.0, 不填写表示无限制'
        />
      </ProForm>
    </Modal>
  );
};

export default VersionFormModal;
