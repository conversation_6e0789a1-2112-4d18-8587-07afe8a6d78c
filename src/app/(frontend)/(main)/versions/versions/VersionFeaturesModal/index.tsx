'use client';

import { App, Form, Modal } from 'antd';
import {
  ProForm,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-components';
import { trpcClient, trpcQuery } from '@/libs/trpc/client';
import { useEffect, useMemo, useState } from 'react';

interface VersionFeaturesModalProps {
  open: boolean;
  versionId: string;
  onCancel: () => void;
  onSuccess: () => void;
}

const VersionFeaturesModal: React.FC<VersionFeaturesModalProps> = ({
  open,
  onCancel,
  onSuccess,
  versionId,
}) => {
  const { message } = App.useApp();

  const [form] = Form.useForm();

  const [isLoading, setIsLoading] = useState(false);

  const { data: features } = trpcQuery.feature.getAllFeatures.useQuery();

  // 功能选项
  const featuresOptions = useMemo(() => {
    return (
      features?.data?.map((feature) => ({
        label: `[${feature.belongTo}-${feature.category}]${feature.name} (${feature.code})`,
        value: feature.id,
      })) ?? []
    );
  }, [features]);

  // 提交表单
  const handleSubmit = async () => {
    setIsLoading(true);

    try {
      const values = await form.validateFields();

      const { success } = await trpcClient.version.relateVersionFeatures.mutate(
        {
          versionId,
          ...values,
        }
      );

      if (success) {
        message.success('关联成功');
        onSuccess();
      } else {
        message.error('关联失败');
      }
    } catch (error) {
      console.error('版本操作失败:', error);
      message.error('操作失败，请稍后重试');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Modal
      destroyOnHidden
      title={'关联功能'}
      open={open}
      onOk={handleSubmit}
      onCancel={onCancel}
      width={600}
      confirmLoading={isLoading}
    >
      <ProForm
        submitter={false}
        form={form}
        request={async () => {
          const { data } =
            await trpcClient.version.getVersionFeatures.query(versionId);

          setTimeout(() => {
            form.setFieldsValue(data);
          });

          return data;
        }}
      >
        <ProFormText hidden name='versionId' />

        <ProFormSelect
          name='grantFeatures'
          label='授予功能'
          fieldProps={{
            mode: 'multiple',
            options: featuresOptions,
          }}
        />

        <ProFormSelect
          name='revokeFeatures'
          label='撤销功能'
          fieldProps={{
            mode: 'multiple',
            options: featuresOptions,
          }}
        />
      </ProForm>
    </Modal>
  );
};

export default VersionFeaturesModal;
