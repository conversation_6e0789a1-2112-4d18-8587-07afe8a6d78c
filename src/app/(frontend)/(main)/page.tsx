'use client';

import { Card, Col, Row, Typography } from 'antd';
import { useTheme } from 'antd-style';
import Link from 'next/link';
import { CSSProperties } from 'react';
import { Flexbox } from 'react-layout-kit';

const { Title, Text } = Typography;

interface ModuleCard {
  title: string;
  description: string;
  icon: string;
  path: string;
  color: string;
}

const modules: ModuleCard[] = [
  {
    title: '功能管理',
    description: '配置功能元数据信息',
    icon: '⚙️',
    path: '/features',
    color: '#52c41a',
  },
  {
    title: '版本管理',
    description: '将功能聚合成版本，并设置版本功能权限和用户限制',
    icon: '📦',
    path: '/versions',
    color: '#fa8c16',
  },
  {
    title: '组织管理',
    description: '维护组织详情和相关联系人信息',
    icon: '🏢',
    path: '/organizations',
    color: '#722ed1',
  },
  {
    title: 'License 管理',
    description: '查看授权信息，配置许可证与组织/版本的关联关系',
    icon: '🔑',
    path: '/license',
    color: '#1890ff',
  },
];

export default function HomePage() {
  const theme = useTheme();

  const cardStyle: CSSProperties = {
    height: '200px',
    cursor: 'pointer',
    transition: 'all 0.3s ease',
    border: `1px solid ${theme.colorBorder}`,
  };

  const cardHoverStyle: CSSProperties = {
    transform: 'translateY(-4px)',
    boxShadow: theme.boxShadowTertiary,
  };

  return (
    <Flexbox gap={24}>
      <Flexbox gap={8}>
        <Title level={2} style={{ margin: 0, color: theme.colorText }}>
          欢迎使用 Lobe Console
        </Title>
        <Text type='secondary' style={{ fontSize: '16px' }}>
          统一管理许可证、组织、版本和功能模块，提供完整的授权管理解决方案
        </Text>
      </Flexbox>

      <Row gutter={[24, 24]}>
        {modules.map((module) => (
          <Col xs={24} sm={12} md={8} lg={6} key={module.path}>
            <Link href={module.path} style={{ textDecoration: 'none' }}>
              <Card
                hoverable
                style={cardStyle}
                styles={{
                  body: {
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'center',
                    alignItems: 'center',
                    textAlign: 'center',
                    padding: '24px 16px',
                  },
                }}
                onMouseEnter={(e) => {
                  Object.assign(e.currentTarget.style, cardHoverStyle);
                }}
                onMouseLeave={(e) => {
                  Object.assign(e.currentTarget.style, cardStyle);
                }}
              >
                <Flexbox gap={16} align='center'>
                  <div
                    style={{
                      fontSize: '48px',
                      marginBottom: '8px',
                      filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.1))',
                    }}
                  >
                    {module.icon}
                  </div>
                  <div>
                    <Title
                      level={4}
                      style={{
                        margin: '0 0 8px 0',
                        color: module.color,
                        fontWeight: 600,
                      }}
                    >
                      {module.title}
                    </Title>
                    <Text
                      type='secondary'
                      style={{
                        fontSize: '14px',
                        lineHeight: '1.5',
                        display: 'block',
                      }}
                    >
                      {module.description}
                    </Text>
                  </div>
                </Flexbox>
              </Card>
            </Link>
          </Col>
        ))}
      </Row>
    </Flexbox>
  );
}
