'use client';

import { App, Form, Modal } from 'antd';
import { BELONG_TO_OPTIONS } from '@/types/feature';
import {
  ProForm,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { trpcClient } from '@/libs/trpc/client';
import { useState } from 'react';

interface FeatureFormModalProps {
  open: boolean;
  featureId?: string;
  onCancel: () => void;
  onSuccess: () => void;
}

const FeatureFormModal: React.FC<FeatureFormModalProps> = ({
  open,
  onCancel,
  onSuccess,
  featureId,
}) => {
  const { message } = App.useApp();

  const [form] = Form.useForm();

  const [isLoading, setIsLoading] = useState(false);

  // 提交表单
  const handleSubmit = async () => {
    setIsLoading(true);

    try {
      const values = await form.validateFields();

      let success;

      if (featureId) {
        success = await trpcClient.feature.updateFeature.mutate({
          id: featureId,
          data: values,
        });
      } else {
        success = await trpcClient.feature.createFeature.mutate(values);
      }

      if (success) {
        message.success(featureId ? '编辑成功' : '添加成功');
        onSuccess();
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Modal
      destroyOnHidden
      title={featureId ? '编辑功能' : '添加功能'}
      open={open}
      onOk={handleSubmit}
      onCancel={onCancel}
      width={600}
      confirmLoading={isLoading}
    >
      <ProForm
        grid
        submitter={false}
        form={form}
        request={async () => {
          if (!featureId) {
            // 如果 featureId 为空，则重置表单
            setTimeout(form.resetFields);
            return {};
          }

          const { data } = await trpcClient.feature.getFeature.query(featureId);

          setTimeout(() => {
            form.setFieldsValue(data);
          });

          return data;
        }}
      >
        <ProFormText
          name='name'
          label='功能名称'
          rules={[{ required: true }]}
        />

        <ProFormText
          name='code'
          label='功能编码'
          rules={[{ required: true }]}
        />

        <ProFormSelect
          name='belongTo'
          label='所属系统'
          rules={[{ required: true, message: '请选择所属系统' }]}
          options={BELONG_TO_OPTIONS.map((option) => ({
            label: option,
            value: option,
          }))}
          colProps={{ span: 12 }}
        />

        <ProFormText name='category' label='功能分类' colProps={{ span: 12 }} />

        <ProFormTextArea
          name='description'
          label='功能描述'
          rules={[{ max: 500, message: '功能描述不能超过500个字符' }]}
        />
      </ProForm>
    </Modal>
  );
};

export default FeatureFormModal;
