'use client';

import { <PERSON><PERSON>, <PERSON><PERSON>, Popconfirm, Space, Tag, Tooltip } from 'antd';
import { ActionType, ProColumns } from '@ant-design/pro-components';
import { ActionIcon } from '@lobehub/ui';
import { Edit3Icon, PlusIcon, Trash } from 'lucide-react';
import { Key, ReactNode, useRef, useState } from 'react';

import Table from '@/components/Table';
import { trpcClient } from '@/libs/trpc/client';
import { Feature } from '@/database/schemas';
import { FeatureFormModal } from './features';
import { BELONG_TO_OPTIONS } from '@/types';

const FeatureManagementPage = () => {
  const { message } = App.useApp();

  const actionRef = useRef<ActionType>(null);

  const [featureId, setFeatureId] = useState<string | undefined>();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<Key[]>([]);

  // 删除功能
  const handleDeleteFeature = async (id: string) => {
    const { success } = await trpcClient.feature.deleteFeature.mutate(id);

    if (success) {
      message.success('删除成功');

      actionRef.current?.reload();
    } else {
      message.error('删除失败');
    }
  };

  // 批量删除功能
  const handleBatchDelete = async (ids: string[]) => {
    const { success } = await trpcClient.feature.batchDeleteFeatures.mutate({
      featureIds: ids,
    });

    if (success) {
      message.success('批量删除成功');

      actionRef.current?.reload();
    } else {
      message.error('批量删除失败');
    }
  };

  // 打开弹窗
  const handleOpenModal = (featureId?: string) => {
    setFeatureId(featureId);
    setIsModalOpen(true);
  };

  // 关闭弹窗
  const handleCloseModal = () => {
    setFeatureId(undefined);
    setIsModalOpen(false);
  };

  // 成功回调
  const handleSuccess = () => {
    handleCloseModal();
    actionRef.current?.reload();
  };

  // 表格列定义
  const columns: ProColumns<Feature>[] = [
    {
      title: '功能名称',
      dataIndex: 'name',
      ellipsis: true,
      copyable: true,
      width: 200,
    },
    {
      title: '功能代号',
      dataIndex: 'code',
      width: 200,
      ellipsis: true,
      copyable: true,
    },
    {
      title: '功能分类',
      dataIndex: 'category',
      width: 120,
      hideInSearch: true,
      ellipsis: true,
    },
    {
      title: '所属系统',
      dataIndex: 'belongTo',
      width: 100,
      valueType: 'select',
      fieldProps: {
        options: BELONG_TO_OPTIONS.map((option) => ({
          label: option,
          value: option,
        })),
      },
      render: (_: ReactNode, record: Feature) => {
        return (
          <Tag color={record.belongTo === 'lobe' ? 'green' : 'yellow'}>
            {record.belongTo}
          </Tag>
        );
      },
    },
    {
      title: '功能描述',
      dataIndex: 'description',
      ellipsis: true,
      search: false,
      hideInSearch: true,
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      width: 180,
      search: false,
      sorter: true,
      valueType: 'dateTime',
    },
    {
      title: '操作',
      valueType: 'option',
      width: 120,
      fixed: 'right',
      render: (_, record) => {
        return (
          <Space size='small'>
            <ActionIcon
              key={`edit-${record.id}`}
              icon={Edit3Icon}
              size='small'
              onClick={() => handleOpenModal(record.id)}
            />
            <Popconfirm
              key={`delete-${record.id}`}
              title='确认删除'
              description='确定要删除该功能吗？'
              onConfirm={() => handleDeleteFeature(record.id)}
              okText='确认'
              cancelText='取消'
            >
              <Tooltip title='删除'>
                <ActionIcon
                  icon={Trash}
                  size='small'
                  style={{ color: '#ff4d4f' }}
                />
              </Tooltip>
            </Popconfirm>
          </Space>
        );
      },
    },
  ];

  return (
    <>
      <Table<Feature>
        columns={columns}
        rowKey='id'
        actionRef={actionRef}
        toolBarRender={() => {
          const actions = [
            <Button
              key='add'
              type='primary'
              icon={<PlusIcon size={16} />}
              onClick={() => handleOpenModal()}
            >
              添加功能
            </Button>,
          ];

          if (selectedRowKeys.length > 0) {
            actions.unshift(
              <Popconfirm
                key='delete'
                title='确认删除'
                description={`确定要删除 ${selectedRowKeys.length} 个功能吗？`}
                onConfirm={() => handleBatchDelete(selectedRowKeys as string[])}
                okText='确认'
                cancelText='取消'
                placement='topRight'
              >
                <Button danger type='primary' icon={<Trash size={16} />}>
                  批量删除
                </Button>
              </Popconfirm>
            );
          }

          return actions;
        }}
        options={false}
        rowSelection={{
          selectedRowKeys,
          onChange: setSelectedRowKeys,
        }}
        request={async (params, sort) => {
          const { data } = await trpcClient.feature.queryFeatures.query({
            ...params,
            sort,
            page: params.current,
            pageSize: params.pageSize,
          });

          return {
            data: data.data,
            total: data.total,
            success: true,
          };
        }}
      />
      <FeatureFormModal
        open={isModalOpen}
        featureId={featureId}
        onCancel={handleCloseModal}
        onSuccess={handleSuccess}
      />
    </>
  );
};

export default FeatureManagementPage;
