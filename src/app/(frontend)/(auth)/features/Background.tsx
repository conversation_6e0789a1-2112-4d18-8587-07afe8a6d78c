'use client';

import { useTheme } from 'antd-style';
import { PropsWithChildren, memo } from 'react';
import { Center, Flexbox } from 'react-layout-kit';

import { ProductLogo } from '@/components/Branding';

const COPYRIGHT = `© ${new Date().getFullYear()} LobeHub, LLC`;

const Background = memo<PropsWithChildren>(({ children }) => {
  const theme = useTheme();
  return (
    <>
      <Flexbox
        align={'center'}
        horizontal
        justify={'space-between'}
        width={'100%'}
      >
        <Flexbox align={'center'} horizontal>
          <ProductLogo extra={'Console'} size={36} type={'combine'} />
        </Flexbox>
      </Flexbox>
      <Center height={'100%'} width={'100%'}>
        {children}
      </Center>
      <Flexbox
        align={'center'}
        gap={8}
        horizontal
        style={{ color: theme.colorTextDescription }}
        width={'100%'}
      >
        <div>{COPYRIGHT}</div>
        <span>·</span>
      </Flexbox>
    </>
  );
});

Background.displayName = 'Background';

export default Background;
