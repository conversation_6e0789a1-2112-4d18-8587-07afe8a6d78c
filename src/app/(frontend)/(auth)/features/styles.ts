'use client';

import { createStyles } from 'antd-style';

export const useStyles = createStyles(({ css }) => ({
  card: css`
    flex: 1;
  `,
  cardBox: css`
    width: 100%;
    max-width: 100%;
    min-height: 100%;
    border-radius: 0;

    box-shadow: unset;
  `,
  rootBox: css`
    scrollbar-width: none;

    overflow: hidden auto;

    width: 100%;
    height: 100%;
    max-height: 100%;

    ::-webkit-scrollbar {
      width: 0;
      height: 0;
    }
  `,
}));
