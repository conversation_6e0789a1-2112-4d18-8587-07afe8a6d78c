import { AuroraBackground } from '@lobehub/ui/awesome';
import { PropsWithChildren } from 'react';
import { Center } from 'react-layout-kit';

import Background from './features/Background';

const Layout = async ({ children }: PropsWithChildren) => {
  return (
    <AuroraBackground
      height={'100%'}
      padding={32}
      style={{ position: 'relative' }}
      width={'100%'}
    >
      <Background>
        <Center
          gap={120}
          height={'100%'}
          horizontal
          style={{ zIndex: 100 }}
          width={'100%'}
        >
          {children}
        </Center>
      </Background>
    </AuroraBackground>
  );
};

Layout.displayName = 'AuthLayout';

export default Layout;
