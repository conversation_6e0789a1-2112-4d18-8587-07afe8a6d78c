import { NuqsAdapter } from 'nuqs/adapters/next/app';
import { ReactNode } from 'react';

import NProgress from '@/components/NProgress';
import AuthProvider from '@/layouts/AuthProvider';
import GlobalProvider from '@/layouts/GlobalProvider';

interface RootLayoutProps {
  children: ReactNode;
}

const RootLayout = async ({ children }: RootLayoutProps) => {
  return (
    <html lang={'zh-CN'} suppressHydrationWarning>
      <body>
        <NuqsAdapter>
          <GlobalProvider>
            <NProgress />
            <AuthProvider>{children}</AuthProvider>
          </GlobalProvider>
        </NuqsAdapter>
      </body>
    </html>
  );
};

export { generateMetadata } from '../metadata';

RootLayout.displayName = 'RootLayout';

export default RootLayout;
