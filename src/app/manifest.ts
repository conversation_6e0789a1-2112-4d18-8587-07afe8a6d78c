import { kebabCase } from 'lodash-es';
import type { MetadataRoute } from 'next';

import { BRANDING_NAME } from '@/const/branding';
import { manifestModule } from '@/server/manifest';

const manifest = (): MetadataRoute.Manifest => {
  return manifestModule.generate({
    description: `Dashboard Console for ${BRANDING_NAME}`,
    icons: [
      {
        purpose: 'any',
        sizes: '192x192',
        url: '/icons/icon-192x192.png',
      },
      {
        purpose: 'any',
        sizes: '512x512',
        url: '/icons/icon-512x512.png',
      },
    ],
    id: kebabCase([BRANDING_NAME, 'console'].join('-')),
    name: `${BRANDING_NAME} Console`,
  });
};

export default manifest;
