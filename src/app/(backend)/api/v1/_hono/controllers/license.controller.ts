import { Context } from 'hono';
import { BaseController } from '../common/base.controller';
import { LicenseService } from '../services/license.service';

/**
 * License Controller
 * 处理许可证相关的HTTP请求
 */
export class LicenseController extends BaseController {
  private licenseService: LicenseService | null = null;

  /**
   * 获取许可证服务实例
   */
  private async getLicenseService(): Promise<LicenseService> {
    if (!this.licenseService) {
      const db = await this.getDatabase();
      this.licenseService = new LicenseService(db);
    }
    return this.licenseService;
  }

  /**
   * 检查许可证有效性
   * @param c Hono Context
   */
  async validateLicense(c: Context): Promise<Response> {
    try {
      const { licenseId } = this.getParams<{ licenseId: string }>(c);

      const licenseService = await this.getLicenseService();
      const validation = await licenseService.validateLicense(licenseId);

      return this.success(c, validation);
    } catch (error) {
      return this.handleError(c, error);
    }
  }

  /**
   * 获取许可证对应的功能授权列表
   * @param c Hono Context
   */
  async getLicenseFeatures(c: Context): Promise<Response> {
    try {
      const { licenseId } = this.getParams<{ licenseId: string }>(c);

      const licenseService = await this.getLicenseService();
      const features = await licenseService.getLicenseFeatures(licenseId);

      return this.success(c, features);
    } catch (error) {
      return this.handleError(c, error);
    }
  }
}

// 导出控制器实例的方法
const licenseController = new LicenseController();

export const validateLicense = (c: Context) => licenseController.validateLicense(c);
export const getLicenseFeatures = (c: Context) => licenseController.getLicenseFeatures(c);