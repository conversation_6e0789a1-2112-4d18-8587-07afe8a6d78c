import { Hono } from 'hono';
import {
  validateLicense,
  getLicenseFeatures,
} from '../controllers/license.controller';

const licenseRoutes = new Hono();

// 检查许可证有效性
// GET /api/v1/license/:licenseId/validate
licenseRoutes.get('/:licenseId/validate', validateLicense);

// 获取许可证功能授权列表
// GET /api/v1/license/:licenseId/features
licenseRoutes.get('/:licenseId/features', getLicenseFeatures);

export default licenseRoutes;

