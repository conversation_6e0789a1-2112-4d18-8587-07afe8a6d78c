import { z } from 'zod';

// 请求参数验证 Schema
export const ValidateLicenseRequestSchema = z.object({
  licenseId: z.string().min(1, '许可证ID不能为空'),
});

// 许可证验证响应 Schema
export const ValidateLicenseResponseSchema = z.object({
  success: z.boolean(),
  data: z.object({
    isValid: z.boolean(),
    status: z.enum(['valid', 'expired', 'disabled', 'pending']),
    message: z.string().optional(),
    expiresIn: z.number().optional(),
  }),
  timestamp: z.string(),
});

// 许可证功能授权响应 Schema
export const LicenseFeaturesResponseSchema = z.object({
  success: z.boolean(),
  data: z.object({
    licenseId: z.string(),
    versionId: z.string(),
    grantFeatures: z.array(z.string()),
    revokeFeatures: z.array(z.string()),
  }),
  timestamp: z.string(),
});

// TypeScript 类型定义
export type ValidateLicenseRequest = z.infer<typeof ValidateLicenseRequestSchema>;
export type ValidateLicenseResponse = z.infer<typeof ValidateLicenseResponseSchema>;
export type LicenseFeaturesResponse = z.infer<typeof LicenseFeaturesResponseSchema>;
