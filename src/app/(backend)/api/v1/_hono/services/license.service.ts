import { LicenseValidation } from '@/types/license';
import { ServiceResult } from '../types';
import { BaseService } from '../common/base.service';
import { LobeConsoleDatabase } from '@/database/type';
import { eq } from 'drizzle-orm';
import { feature } from '@/database/schemas/feature';
import { license } from '@/database/schemas/license';
import { versionFeature } from '@/database/schemas/version';

export interface LicenseFeature {
  name: string;
  code: string;
  category: string | null;
  belongTo: 'lobe' | 'admin' | null;
}

export interface LicenseFeaturesResult {
  licenseId: string;
  versionId?: string;
  grantFeatures?: LicenseFeature[];
  revokeFeatures?: LicenseFeature[];
}

/**
 * License Service
 * 处理许可证相关的业务逻辑
 */
export class LicenseService extends BaseService {
  constructor(db: LobeConsoleDatabase) {
    super(db);
  }

  /**
   * 验证许可证有效性
   * @param licenseId 许可证ID
   * @returns 验证结果
   */
  async validateLicense(licenseId: string): ServiceResult<LicenseValidation> {
    try {
      this.log('info', '开始验证许可证', { licenseId });

      if (!licenseId) {
        throw this.createBusinessError('许可证ID不能为空');
      }

      // 直接查询许可证信息
      const licenseInfo = await this.db.query.license.findFirst({
        where: eq(license.id, licenseId),
      });

      if (!licenseInfo) {
        const validation: LicenseValidation = {
          isValid: false,
          status: 'expired', // 使用 expired 表示不存在
          message: '许可证不存在',
        };
        this.log('info', '许可证验证完成', { licenseId, validation });
        return validation;
      }

      const now = new Date();
      const startTime = new Date(licenseInfo.startTime);
      const endTime = new Date(licenseInfo.endTime);

      // 检查许可证状态
      let status: LicenseValidation['status'];
      let isValid = false;
      let message = '';
      let expiresIn: number | undefined;

      if (!licenseInfo.enabled) {
        status = 'disabled';
        message = '许可证已被禁用';
      } else if (now < startTime) {
        status = 'pending';
        message = '许可证尚未生效';
      } else if (now > endTime) {
        status = 'expired';
        message = '许可证已过期';
      } else {
        status = 'valid';
        isValid = true;
        message = '许可证有效';
        // 计算距离过期的天数
        expiresIn = Math.ceil(
          (endTime.getTime() - now.getTime()) / (1000 * 60 * 60 * 24)
        );
      }

      const validation: LicenseValidation = {
        isValid,
        status,
        message,
        expiresIn,
      };

      this.log('info', '许可证验证完成', { licenseId, validation });

      return validation;
    } catch (error) {
      this.handleServiceError(error, '验证许可证', '许可证验证失败');
    }
  }

  /**
   * 获取许可证对应的功能授权列表
   * @param licenseId 许可证ID
   * @returns 功能授权列表
   */
  async getLicenseFeatures(
    licenseId: string
  ): ServiceResult<LicenseFeaturesResult> {
    try {
      this.log('info', '开始获取许可证功能列表', { licenseId });

      if (!licenseId) {
        throw this.createBusinessError('许可证ID不能为空');
      }

      // 检查许可证是否存在
      const licenseExists = await this.db.query.license.findFirst({
        where: eq(license.id, licenseId),
      });

      if (!licenseExists) {
        const error = new Error('许可证不存在');
        error.name = 'NotFoundError';
        throw error;
      }

      // 数据库关联查询一把查出来license关联的版本和功能
      const allVersionFeatures = await this.db
        .select({
          licenseId: license.id,
          versionId: license.versionId,
          grantType: versionFeature.grantType,
          featureName: feature.name,
          featureCode: feature.code,
          featureCategory: feature.category,
          featureBelongTo: feature.belongTo,
        })
        .from(license)
        .innerJoin(
          versionFeature,
          eq(versionFeature.versionId, license.versionId)
        )
        .innerJoin(feature, eq(feature.id, versionFeature.featureId))
        .where(eq(license.id, licenseId));

      // 分组处理授权和撤销的功能
      const grantFeatures: LicenseFeature[] = [];
      const revokeFeatures: LicenseFeature[] = [];
      let versionId = '';

      allVersionFeatures.forEach((row) => {
        versionId = row.versionId;
        const featureData: LicenseFeature = {
          name: row.featureName,
          code: row.featureCode,
          category: row.featureCategory,
          belongTo: row.featureBelongTo,
        };

        if (row.grantType === 'grant') {
          grantFeatures.push(featureData);
        } else if (row.grantType === 'revoke') {
          revokeFeatures.push(featureData);
        }
      });

      const result: LicenseFeaturesResult = {
        licenseId,
        versionId,
        grantFeatures,
        revokeFeatures,
      };

      this.log('info', '许可证功能列表获取完成', { licenseId, result });

      return result;
    } catch (error) {
      this.handleServiceError(
        error,
        '获取许可证功能列表',
        '获取许可证功能列表失败'
      );
    }
  }
}
