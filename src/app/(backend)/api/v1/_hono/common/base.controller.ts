import { Context } from 'hono';
import { HTTPException } from 'hono/http-exception';

import { getServerDB } from '@/database/core/db-adaptor';
import { LobeConsoleDatabase } from '@/database/type';

import { ApiResponse } from '../types';
import { ContentfulStatusCode } from 'hono/utils/http-status';

/**
 * Base Controller Class
 * Provides unified response formatting, error handling, and common utility methods
 */
export abstract class BaseController {
  private _db: LobeConsoleDatabase | null = null;

  /**
   * Get database connection instance
   * Lazy initialization to avoid initializing the database during module import
   */
  protected async getDatabase(): Promise<LobeConsoleDatabase> {
    if (!this._db) {
      this._db = await getServerDB();
    }

    return this._db;
  }

  /**
   * Success response formatting
   * @param c Hono Context
   * @param data Response data
   * @param message Response message
   * @returns Formatted success response
   */
  protected success<T>(c: Context, data?: T, message?: string): Response {
    const response: ApiResponse<T> = {
      data,
      message,
      success: true,
      timestamp: new Date().toISOString(),
    };

    return c.json(response);
  }

  /**
   * Error response formatting
   * @param c Hono Context
   * @param error Error message
   * @param statusCode HTTP status code, default 500
   * @returns Formatted error response
   */
  protected error(
    c: Context,
    error: string,
    statusCode: ContentfulStatusCode = 500
  ): Response {
    const response: ApiResponse = {
      error,
      success: false,
      timestamp: new Date().toISOString(),
    };

    return c.json(response, statusCode);
  }

  /**
   * Unified exception handling
   * @param c Hono Context
   * @param error Exception object
   * @returns Formatted error response
   */
  protected handleError(c: Context, error: unknown): Response {
    console.error('Controller Error:', error);

    // Handle HTTPException
    if (error instanceof HTTPException) {
      return this.error(c, error.message, error.status);
    }

    // Handle other known error types
    if (error instanceof Error) {
      // Handle business logic errors
      if (error.name === 'BusinessError') {
        return this.error(c, error.message, 400);
      }

      // Handle authentication errors
      if (error.name === 'AuthenticationError') {
        return this.error(c, error.message, 401);
      }

      // Handle authorization errors
      if (error.name === 'AuthorizationError') {
        return this.error(c, error.message, 403);
      }

      // Handle not found errors
      if (error.name === 'NotFoundError') {
        return this.error(c, error.message, 404);
      }

      // Other errors
      return this.error(c, error.message, 500);
    }

    // Unknown error
    return this.error(c, 'Internal Server Error', 500);
  }

  /**
   * Get request parameters
   * @param c Hono Context
   * @returns Request parameters object
   */
  protected getParams<T = unknown>(c: Context): T {
    return c.req.param() as T;
  }

  /**
   * Get query parameters
   * @param c Hono Context
   * @returns Query parameters object
   */
  protected getQuery<T = unknown>(c: Context): T {
    return c.req.query() as T;
  }

  /**
   * Get request body
   * @param c Hono Context
   * @returns Request body object
   */
  protected async getBody<T = unknown>(c: Context): Promise<T> {
    return c.req.json() as T;
  }

  /**
   * Get request form data
   * @param c Hono Context
   * @returns Request form data object
   */
  protected async getFormData<T = unknown>(c: Context): Promise<T> {
    return c.req.formData() as T;
  }
}
