'use client';

import { createStyles } from 'antd-style';
import { memo } from 'react';
import { Flexbox, FlexboxProps } from 'react-layout-kit';

import { useUserStore } from '@/store/user';
import { userProfileSelectors } from '@/store/user/selectors';

import UserAvatar, { type UserAvatarProps } from './UserAvatar';

const useStyles = createStyles(({ css, token }) => ({
  nickname: css`
    font-size: 16px;
    font-weight: bold;
    line-height: 1;
  `,
  username: css`
    line-height: 1;
    color: ${token.colorTextDescription};
  `,
}));

export interface UserInfoProps extends FlexboxProps {
  avatarProps?: Partial<UserAvatarProps>;
  onClick?: () => void;
  size?: 'small' | 'normal';
}

const UserInfo = ({
  size = 'normal',
  avatarProps,
  onClick,
  ...rest
}: UserInfoProps) => {
  const { styles, theme } = useStyles();
  const [nickname, username] = useUserStore((s) => [
    userProfileSelectors.nickName(s),
    userProfileSelectors.username(s),
  ]);

  const isSmall = size === 'small';

  return (
    <Flexbox
      align={'center'}
      gap={12}
      horizontal
      justify={'space-between'}
      paddingBlock={12}
      paddingInline={12}
      {...rest}
    >
      <Flexbox
        align={'center'}
        gap={isSmall ? 8 : 12}
        horizontal
        onClick={onClick}
        style={{ cursor: 'pointer' }}
      >
        <UserAvatar
          background={theme.colorFill}
          size={isSmall ? 28 : 48}
          {...avatarProps}
        />
        <Flexbox flex={1} gap={6}>
          <div className={isSmall ? undefined : styles.nickname}>
            {nickname}
          </div>
          {!isSmall && <div className={styles.username}>{username}</div>}
        </Flexbox>
      </Flexbox>
    </Flexbox>
  );
};

export default UserInfo;
