'use client';

import { Clerk<PERSON>rov<PERSON> } from '@clerk/nextjs';
import { PropsWithChildren, useEffect, useState, useTransition } from 'react';

import UserUpdater from './UserUpdater';
import { useAppearance } from './useAppearance';

const Clerk = ({ children }: PropsWithChildren) => {
  const appearance = useAppearance();

  // When useAppearance returns different result during SSR vs. client-side (when theme mode is auto), the appearance is not applied
  // It's because Clerk internally re-applies SSR props after transition which overrides client-side props, see https://github.com/clerk/javascript/blob/main/packages/nextjs/src/app-router/client/ClerkProvider.tsx
  // This re-renders the provider after transition to make sure client-side props are always applied
  const [count, setCount] = useState(0);
  const [isPending, startTransition] = useTransition();
  useEffect(() => {
    if (count || isPending) return;
    startTransition(() => {
      setCount((count) => count + 1);
    });
  }, [count, setCount, isPending, startTransition]);

  return (
    <ClerkProvider
      appearance={{
        ...appearance,
        elements: {
          ...appearance,
          footerAction: { display: 'none' },
        },
      }}
      signUpUrl={'/login'}
    >
      {children}
      <UserUpdater />
    </ClerkProvider>
  );
};

export default Clerk;
