'use client';

import dynamic from 'next/dynamic';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { PropsWithChildren } from 'react';

import LayoutLoading from '@/components/LayoutLoading';
import { useRoutes } from '@/routes';
import { useUserStore } from '@/store/user';
import { userProfileSelectors } from '@/store/user/selectors';

import Avatar from './features/Avatar';
import Logo from './features/Logo';
import { useStyles } from './styles';

const ProLayout = dynamic(
  () => import('@ant-design/pro-components').then((mod) => mod.ProLayout),
  {
    loading: LayoutLoading,
    ssr: false,
  }
);

const MainLayout = ({ children }: PropsWithChildren) => {
  const routers = useRoutes();
  const { styles } = useStyles();
  const pathname = usePathname();
  const user = useUserStore(userProfileSelectors.userProfile);
  const clerkOpenUserProfile = useUserStore((s) => s.clerkOpenUserProfile);

  return (
    <ProLayout
      avatarProps={{
        onClick: () => {
          clerkOpenUserProfile?.();
        },
        render: (_, __, { collapsed }) => <Avatar collapsed={collapsed} />,
        title: user?.fullName || user?.username,
      }}
      className={styles.container}
      location={{ pathname }}
      menu={{
        defaultOpenAll: true,
        ignoreFlatMenu: true,
        locale: false,
      }}
      menuHeaderRender={(_, __, { collapsed } = {}) => (
        <Logo collapsed={collapsed} />
      )}
      menuItemRender={(options, element) => (
        <Link href={options.path!}>{element}</Link>
      )}
      route={routers}
      siderMenuType='group'
      siderWidth={280}
      style={{ width: '100%' }}
      title='Lobe Console'
    >
      {children}
    </ProLayout>
  );
};

export default MainLayout;
