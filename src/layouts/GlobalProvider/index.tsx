import { ReactNode } from 'react';

import QueryProvider from '@/layouts/GlobalProvider/Query';

import AppTheme from './AppTheme';
import StyleRegistry from './StyleRegistry';

interface GlobalLayoutProps {
  children: ReactNode;
}

const GlobalLayout = async ({ children }: GlobalLayoutProps) => {
  return (
    <StyleRegistry>
      <AppTheme>
        <QueryProvider>{children}</QueryProvider>
      </AppTheme>
    </StyleRegistry>
  );
};

export default GlobalLayout;
