'use client';

import '@ant-design/v5-patch-for-react-19';
import { Config<PERSON><PERSON>ider, ThemeProvider } from '@lobehub/ui';
import { createStyles } from 'antd-style';
import 'antd/dist/reset.css';
import Image from 'next/image';
import Link from 'next/link';
import { ReactNode, useEffect, useState } from 'react';

import AntdStaticMethods from '@/components/AntdStaticMethods';
import { GlobalStyle } from '@/styles';

const useStyles = createStyles(({ css, token }) => ({
  app: css`
    position: relative;

    overscroll-behavior: none;
    display: flex;
    flex-direction: column;
    align-items: center;

    height: 100%;
    min-height: 100dvh;
    max-height: 100dvh;

    @media (min-device-width: 576px) {
      overflow: hidden;
    }
  `,
  // scrollbar-width and scrollbar-color are supported from Chrome 121
  // https://developer.mozilla.org/en-US/docs/Web/CSS/scrollbar-color
  scrollbar: css`
    scrollbar-color: ${token.colorFill} transparent;
    scrollbar-width: thin;

    #lobe-mobile-scroll-container {
      scrollbar-width: none;

      ::-webkit-scrollbar {
        width: 0;
        height: 0;
      }
    }
  `,

  // so this is a polyfill for older browsers
  scrollbarPolyfill: css`
    ::-webkit-scrollbar {
      width: 0.75em;
      height: 0.75em;
    }

    ::-webkit-scrollbar-thumb {
      border-radius: 10px;
    }

    :hover::-webkit-scrollbar-thumb {
      border: 3px solid transparent;
      background-color: ${token.colorText};
      background-clip: content-box;
    }

    ::-webkit-scrollbar-track {
      background-color: transparent;
    }
  `,
}));

export interface AppThemeProps {
  children?: ReactNode;
}

const AppTheme = ({ children }: AppThemeProps) => {
  const { styles, cx } = useStyles();
  const [mounted, setMounted] = useState(false);

  // 确保组件已挂载到客户端
  useEffect(() => {
    setMounted(true);
  }, []);

  // 在服务端或首次渲染时，使用 light 主题避免 hydration 错误
  // 客户端挂载后，切换到 auto 模式
  const themeMode = mounted ? 'auto' : 'light';

  return (
    <div suppressHydrationWarning>
      <ThemeProvider
        className={cx(styles.app, styles.scrollbar, styles.scrollbarPolyfill)}
        themeMode={themeMode}
      >
        <GlobalStyle />
        <AntdStaticMethods />
        <ConfigProvider
          config={{
            aAs: Link,
            imgAs: Image,
            imgUnoptimized: true,
          }}
        >
          {children}
        </ConfigProvider>
      </ThemeProvider>
    </div>
  );
};

AppTheme.displayName = 'AppTheme';

export default AppTheme;
