import { useResponsive, useTheme } from 'antd-style';
import Link from 'next/link';

import { ProductLogo } from '@/components/Branding';
import { BRANDING_NAME } from '@/const/branding';

const Logo = ({ collapsed }: { collapsed?: boolean }) => {
  const theme = useTheme();
  const { mobile } = useResponsive();
  return (
    <Link
      href='/'
      style={{ color: theme.colorText }}
      title={`${BRANDING_NAME} 管理后台`}
    >
      <ProductLogo
        extra={collapsed ? undefined : 'Console'}
        size={28}
        style={{ flex: 'none' }}
        type={collapsed || mobile ? '3d' : 'combine'}
      />
    </Link>
  );
};

Logo.displayName = 'Logo';

export default Logo;
