import { MenuDataItem, Route } from '@ant-design/pro-layout/es/typing';
import { Icon, IconProps } from '@lobehub/ui';
import {
  Building2,
  HomeIcon,
  Settings,
  Signature,
  Package,
} from 'lucide-react';
import { useMemo } from 'react';

export enum Routes {
  Console = '/',
  License = '/license',
  Features = '/features',
  Versions = '/versions',
  Organizations = '/organizations',
}
const size: IconProps['size'] = { size: 16, strokeWidth: 2.5 };

export const useRoutes = (): Route => {
  const routes: MenuDataItem[] = useMemo(
    () => [
      {
        icon: <Icon icon={HomeIcon} size={size} />,
        key: Routes.Console,
        name: '控制台',
        path: Routes.Console,
      },
      {
        icon: <Icon icon={Settings} size={size} />,
        key: Routes.Features,
        name: '功能管理',
        path: Routes.Features,
      },
      {
        icon: <Icon icon={Package} size={size} />,
        key: Routes.Versions,
        name: '版本管理',
        path: Routes.Versions,
      },
      {
        icon: <Icon icon={Building2} size={size} />,
        key: Routes.Organizations,
        name: '组织管理',
        path: Routes.Organizations,
      },
      {
        icon: <Icon icon={Signature} size={size} />,
        key: Routes.License,
        name: 'License 管理',
        path: Routes.License,
      },
    ],
    []
  );

  return useMemo(
    () => ({
      path: '/',
      routes,
    }),
    [routes]
  );
};
