import { eq, and, SQL, inArray, count, like } from 'drizzle-orm';
import { generateOrderBy } from '@/database/core/order-utils';

import { serverDB } from '@/database/core/db-adaptor';
import {
  version,
  versionFeature,
  Version,
  InsertVersion,
  VersionFeature,
  InsertVersionFeature,
} from '@/database/schemas';
import { VersionQueryConditions } from '@/types';

export class VersionModel {
  /**
   * 构建版本查询条件
   * @param conditions 查询条件
   * @returns SQL 查询条件
   */
  private buildVersionWhereConditions = (
    conditions: VersionQueryConditions
  ): SQL | undefined => {
    const whereConditions: SQL[] = [];

    if (conditions.id !== undefined) {
      whereConditions.push(eq(version.id, conditions.id));
    }

    if (conditions.name !== undefined) {
      whereConditions.push(like(version.name, `%${conditions.name}%`));
    }

    if (conditions.isActive !== undefined) {
      whereConditions.push(eq(version.isActive, conditions.isActive));
    }

    if (whereConditions.length === 0) {
      return undefined;
    }

    return whereConditions.length === 1
      ? whereConditions[0]
      : and(...whereConditions);
  };

  /**
   * 根据条件查询版本
   * @param conditions 查询条件
   * @returns 版本列表和总数
   */
  queryVersions = async (
    conditions: VersionQueryConditions
  ): Promise<{ data: Version[]; total: number }> => {
    const whereCondition = this.buildVersionWhereConditions(conditions);

    const result = await serverDB.query.version.findMany({
      where: whereCondition,
      orderBy: conditions.sort
        ? generateOrderBy(conditions.sort, version)
        : [version.createdAt],
      limit: conditions.pageSize,
      offset: ((conditions.page ?? 1) - 1) * (conditions.pageSize ?? 10),
    });

    const total = await serverDB
      .select({ count: count() })
      .from(version)
      .where(whereCondition);

    return {
      data: result,
      total: total[0].count,
    };
  };

  /**
   * 根据条件查询单个版本
   * @param conditions 查询条件
   * @returns 版本详情或 null
   */
  queryVersion = async (
    conditions: Omit<VersionQueryConditions, 'page' | 'pageSize'>
  ): Promise<Version | null> => {
    const whereCondition = this.buildVersionWhereConditions(conditions);

    const result = await serverDB.query.version.findFirst({
      where: whereCondition,
    });

    return result || null;
  };

  /**
   * 查询版本及其关联的功能
   * @param versionId 版本ID
   * @returns 版本信息及功能权限列表
   */
  queryVersionWithFeatures = async (versionId: string) => {
    const result = await serverDB.query.version.findFirst({
      where: eq(version.id, versionId),
      with: {
        versionFeatures: true,
      },
    });

    return result;
  };

  /**
   * 创建版本
   * @param data 创建数据
   * @returns 创建的版本信息
   */
  createVersion = async (data: InsertVersion): Promise<Version> => {
    const result = await serverDB.insert(version).values(data).returning();
    return result[0];
  };

  /**
   * 更新版本信息
   * @param versionId 版本 ID
   * @param data 更新数据
   * @returns 更新后的版本信息或 null
   */
  updateVersion = async (
    versionId: string,
    data: Partial<InsertVersion>
  ): Promise<Version | null> => {
    const result = await serverDB
      .update(version)
      .set(data)
      .where(eq(version.id, versionId))
      .returning();

    return result.length > 0 ? result[0] : null;
  };

  /**
   * 删除版本
   * @param versionId 版本 ID
   * @returns 是否删除成功
   */
  deleteVersion = async (versionId: string): Promise<boolean> => {
    // 先删除相关的功能关联
    await serverDB
      .delete(versionFeature)
      .where(eq(versionFeature.versionId, versionId));

    // 再删除版本
    const result = await serverDB
      .delete(version)
      .where(eq(version.id, versionId))
      .returning();

    return result.length > 0;
  };

  /**
   * 批量删除版本
   * @param versionIds 版本 ID 数组
   * @returns 是否删除成功
   */
  batchDeleteVersions = async (versionIds: string[]): Promise<boolean> => {
    // 先删除相关的功能关联
    await serverDB
      .delete(versionFeature)
      .where(inArray(versionFeature.versionId, versionIds));

    // 再删除版本
    const result = await serverDB
      .delete(version)
      .where(inArray(version.id, versionIds))
      .returning();

    return result.length > 0;
  };

  // === 版本功能关联相关方法 ===

  /**
   * 批量创建版本功能关联
   * @param dataList 批量创建数据
   * @returns 创建的关联信息列表
   */
  batchCreateVersionFeatures = async (
    dataList: InsertVersionFeature[]
  ): Promise<VersionFeature[]> => {
    const result = await serverDB
      .insert(versionFeature)
      .values(dataList)
      .returning();

    return result;
  };

  /**
   * 删除版本的所有功能关联
   * @param versionId 版本ID
   * @returns 是否删除成功
   */
  deleteAllVersionFeatures = async (versionId: string): Promise<boolean> => {
    const result = await serverDB
      .delete(versionFeature)
      .where(eq(versionFeature.versionId, versionId))
      .returning();

    return result.length >= 0; // 即使没有关联也算成功
  };

  /**
   * 获取版本关联的功能
   * @param versionId 版本ID
   * @returns 版本关联的功能列表
   */
  getVersionFeatures = async (
    versionId: string
  ): Promise<{
    versionId: string;
    grantFeatures: string[];
    revokeFeatures: string[];
  } | null> => {
    const version = await this.queryVersion({ id: versionId });
    if (!version) return null;

    const features = await serverDB.query.versionFeature.findMany({
      where: eq(versionFeature.versionId, versionId),
    });

    const grantFeatures: string[] = [];
    const revokeFeatures: string[] = [];

    features.forEach((feature) => {
      if (feature.grantType === 'grant') {
        grantFeatures.push(feature.featureId);
      } else if (feature.grantType === 'revoke') {
        revokeFeatures.push(feature.featureId);
      }
    });

    return {
      versionId,
      grantFeatures,
      revokeFeatures,
    };
  };

  /**
   * 获取所有版本列表
   * @returns 所有版本列表
   */
  getAllVersions = async (): Promise<Version[]> => {
    const result = await serverDB.query.version.findMany({
      orderBy: [version.createdAt],
    });

    return result;
  };
}
