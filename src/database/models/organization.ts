import {
  eq,
  and,
  S<PERSON>,
  isNull,
  isNotNull,
  count,
  AnyColumn,
  like,
} from 'drizzle-orm';

import { serverDB } from '@/database/core/db-adaptor';
import {
  organization,
  Organization,
  InsertOrganization,
} from '@/database/schemas';
import { OrganizationQueryConditions } from '@/types/organization';
import { generateOrderBy } from '@/database/core/order-utils';

export class OrganizationModel {
  /**
   * 构建查询条件
   * @param conditions 查询条件
   * @returns SQL 查询条件
   */
  private buildWhereConditions = (
    conditions: OrganizationQueryConditions
  ): SQL | undefined => {
    const whereConditions: SQL[] = [];

    if (conditions.id !== undefined) {
      whereConditions.push(eq(organization.id, conditions.id));
    }

    if (conditions.chineseName !== undefined) {
      whereConditions.push(
        like(organization.chineseName, `%${conditions.chineseName}%`)
      );
    }

    if (conditions.englishName !== undefined) {
      whereConditions.push(
        like(organization.englishName, `%${conditions.englishName}%`)
      );
    }

    if (conditions.country !== undefined) {
      whereConditions.push(
        like(organization.country, `%${conditions.country}%`)
      );
    }

    if (whereConditions.length === 0) {
      return undefined;
    }

    return whereConditions.length === 1
      ? whereConditions[0]
      : and(...whereConditions);
  };

  /**
   * 根据条件查询组织
   * @param conditions 查询条件
   * @returns 组织列表和总数
   */
  queryOrganizations = async (
    conditions: OrganizationQueryConditions = {}
  ): Promise<{ data: Organization[]; total: number }> => {
    const whereCondition = this.buildWhereConditions(conditions);

    const result = await serverDB.query.organization.findMany({
      where: whereCondition,
      orderBy: conditions.sort
        ? generateOrderBy(conditions.sort, organization)
        : [organization.createdAt],
      limit: conditions.pageSize,
      offset: ((conditions.page ?? 1) - 1) * (conditions.pageSize ?? 10),
    });

    const total = await serverDB
      .select({ count: count() })
      .from(organization)
      .where(whereCondition);

    return {
      data: result,
      total: total[0].count,
    };
  };

  getAllOrganizations = async (): Promise<Organization[]> => {
    const result = await serverDB.query.organization.findMany();
    return result;
  };

  /**
   * 根据条件查询单个组织
   * @param conditions 查询条件
   * @returns 组织详情或 null
   */
  queryOrganization = async (
    conditions: OrganizationQueryConditions
  ): Promise<Organization | null> => {
    const whereCondition = this.buildWhereConditions(conditions);

    const organization = await serverDB.query.organization.findFirst({
      where: whereCondition,
    });

    return organization || null;
  };

  /**
   * 创建组织
   * @param data 创建数据
   * @returns 创建的组织信息
   */
  createOrganization = async (
    data: InsertOrganization
  ): Promise<Organization> => {
    const result = await serverDB.insert(organization).values(data).returning();

    return result[0];
  };

  /**
   * 更新组织信息
   * @param organizationId 组织 ID
   * @param data 更新数据
   * @returns 更新后的组织信息或 null
   */
  updateOrganization = async (
    organizationId: string,
    data: Partial<InsertOrganization>
  ): Promise<Organization | null> => {
    const result = await serverDB
      .update(organization)
      .set(data)
      .where(eq(organization.id, organizationId))
      .returning();

    return result.length > 0 ? result[0] : null;
  };

  /**
   * 删除组织
   * @param organizationId 组织 ID
   * @returns 是否删除成功
   */
  deleteOrganization = async (organizationId: string): Promise<boolean> => {
    const result = await serverDB
      .delete(organization)
      .where(eq(organization.id, organizationId))
      .returning();

    return result.length > 0;
  };
}
