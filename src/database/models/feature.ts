import { eq, and, SQL, inArray, count, like } from 'drizzle-orm';

import { serverDB } from '@/database/core/db-adaptor';
import { feature, Feature, InsertFeature } from '@/database/schemas';
import { generateOrderBy } from '@/database/core/order-utils';
import { FeatureQueryConditions } from '@/types';

export class FeatureModel {
  /**
   * 构建查询条件
   * @param conditions 查询条件
   * @returns SQL 查询条件
   */
  private buildWhereConditions = (
    conditions: FeatureQueryConditions
  ): SQL | undefined => {
    const whereConditions: SQL[] = [];

    if (conditions.id !== undefined) {
      whereConditions.push(eq(feature.id, conditions.id));
    }

    if (conditions.code !== undefined) {
      whereConditions.push(like(feature.code, `%${conditions.code}%`));
    }

    if (conditions.name !== undefined) {
      whereConditions.push(like(feature.name, `%${conditions.name}%`));
    }

    if (conditions.category !== undefined) {
      whereConditions.push(eq(feature.category, conditions.category));
    }

    if (conditions.belongTo !== undefined) {
      whereConditions.push(eq(feature.belongTo, conditions.belongTo));
    }

    if (whereConditions.length === 0) {
      return undefined;
    }

    return whereConditions.length === 1
      ? whereConditions[0]
      : and(...whereConditions);
  };

  /**
   * 根据条件查询功能
   * @param conditions 查询条件
   * @returns 功能列表或单个功能
   */
  queryFeatures = async (
    conditions: FeatureQueryConditions
  ): Promise<{ data: Feature[]; total: number }> => {
    const whereCondition = this.buildWhereConditions(conditions);

    const result = await serverDB.query.feature.findMany({
      where: whereCondition,
      orderBy: conditions.sort
        ? generateOrderBy(conditions.sort, feature)
        : [feature.createdAt, feature.updatedAt],

      limit: conditions.pageSize,
      offset: ((conditions.page ?? 1) - 1) * (conditions.pageSize ?? 10),
    });

    const total = await serverDB
      .select({ count: count() })
      .from(feature)
      .where(whereCondition);

    return {
      data: result,
      total: total[0].count,
    };
  };

  /**
   * 根据条件查询单个功能
   * @param conditions 查询条件
   * @returns 功能详情或 null
   */
  queryFeature = async (
    conditions: Omit<FeatureQueryConditions, 'page' | 'pageSize'>
  ): Promise<Feature | null> => {
    const whereCondition = this.buildWhereConditions(conditions);

    const result = await serverDB.query.feature.findFirst({
      where: whereCondition,
    });

    return result || null;
  };

  /**
   * 创建功能
   * @param data 创建数据
   * @returns 创建的功能信息
   */
  createFeature = async (data: InsertFeature): Promise<Feature> => {
    const result = await serverDB.insert(feature).values(data).returning();

    return result[0];
  };

  /**
   * 更新功能信息
   * @param featureId 功能 ID
   * @param data 更新数据
   * @returns 更新后的功能信息或 null
   */
  updateFeature = async (
    featureId: string,
    data: Partial<InsertFeature>
  ): Promise<Feature | null> => {
    const result = await serverDB
      .update(feature)
      .set(data)
      .where(eq(feature.id, featureId))
      .returning();

    return result.length > 0 ? result[0] : null;
  };

  /**
   * 删除功能
   * @param featureId 功能 ID
   * @returns 是否删除成功
   */
  deleteFeature = async (featureId: string): Promise<boolean> => {
    const result = await serverDB
      .delete(feature)
      .where(eq(feature.id, featureId))
      .returning();

    return result.length > 0;
  };

  /**
   * 检查功能代号是否存在
   * @param code 功能代号
   * @param excludeId 排除的功能 ID（用于更新时检查）
   * @returns 是否存在
   */
  isCodeExists = async (code: string, excludeId?: string): Promise<boolean> => {
    const result = await serverDB.query.feature.findFirst({
      where: eq(feature.code, code),
    });

    if (!result) return false;
    if (excludeId && result.id === excludeId) return false;

    return true;
  };

  /**
   * 批量删除功能
   * @param featureIds 功能 ID 数组
   * @returns 是否删除成功
   */
  batchDeleteFeatures = async (featureIds: string[]): Promise<boolean> => {
    const result = await serverDB
      .delete(feature)
      .where(inArray(feature.id, featureIds))
      .returning();
    return result.length > 0;
  };

  /**
   * 获取所有功能列表
   * @returns 所有功能列表
   */
  getAllFeatures = async (): Promise<Feature[]> => {
    const result = await serverDB.query.feature.findMany({
      orderBy: [feature.createdAt],
    });

    return result;
  };
}
