import { eq, and, SQL, count } from 'drizzle-orm';

import { serverDB } from '@/database/core/db-adaptor';
import { license, License, InsertLicense } from '@/database/schemas';
import { LicenseQueryConditions, LicenseWithRelations } from '@/types/license';
import { generateOrderBy } from '@/database/core/order-utils';

export class LicenseModel {
  /**
   * 构建查询条件
   * @param conditions 查询条件
   * @returns SQL 查询条件
   */
  private buildWhereConditions = (
    conditions: LicenseQueryConditions
  ): SQL | undefined => {
    const whereConditions: SQL[] = [];

    if (conditions.id !== undefined) {
      whereConditions.push(eq(license.id, conditions.id));
    }

    if (conditions.organizationId !== undefined) {
      whereConditions.push(
        eq(license.organizationId, conditions.organizationId)
      );
    }

    if (conditions.versionId !== undefined) {
      whereConditions.push(eq(license.versionId, conditions.versionId));
    }

    if (conditions.enabled !== undefined) {
      whereConditions.push(eq(license.enabled, conditions.enabled));
    }

    if (whereConditions.length === 0) {
      return undefined;
    }

    return whereConditions.length === 1
      ? whereConditions[0]
      : and(...whereConditions);
  };

  /**
   * 根据条件查询许可证
   * @param conditions 查询条件
   * @returns 许可证列表和总数，包含关联的组织和版本信息
   */
  queryLicenses = async (
    conditions: LicenseQueryConditions = {}
  ): Promise<{ data: LicenseWithRelations[]; total: number }> => {
    const whereCondition = this.buildWhereConditions(conditions);

    const result = await serverDB.query.license.findMany({
      where: whereCondition,
      with: {
        organization: true,
        version: true,
      },
      orderBy: conditions.sort
        ? generateOrderBy(conditions.sort, license)
        : [license.createdAt],
      limit: conditions.pageSize,
      offset: ((conditions.page ?? 1) - 1) * (conditions.pageSize ?? 10),
    });

    const total = await serverDB
      .select({ count: count() })
      .from(license)
      .where(whereCondition);

    return {
      data: result,
      total: total[0].count,
    };
  };

  /**
   * 根据条件查询单个许可证
   * @param conditions 查询条件
   * @returns 许可证详情或 null，包含关联的组织和版本信息
   */
  queryLicense = async (
    conditions: LicenseQueryConditions
  ): Promise<LicenseWithRelations | null> => {
    const whereCondition = this.buildWhereConditions(conditions);

    const result = await serverDB.query.license.findFirst({
      where: whereCondition,
      with: {
        organization: true,
        version: true,
      },
    });

    return result || null;
  };

  /**
   * 创建许可证
   * @param data 创建数据
   * @returns 创建的许可证信息
   */
  createLicense = async (data: InsertLicense): Promise<License> => {
    const result = await serverDB.insert(license).values(data).returning();

    return result[0];
  };

  /**
   * 更新许可证信息
   * @param licenseId 许可证 ID
   * @param data 更新数据
   * @returns 更新后的许可证信息或 null
   */
  updateLicense = async (
    licenseId: string,
    data: Partial<InsertLicense>
  ): Promise<License | null> => {
    const result = await serverDB
      .update(license)
      .set(data)
      .where(eq(license.id, licenseId))
      .returning();

    return result.length > 0 ? result[0] : null;
  };

  /**
   * 删除许可证
   * @param licenseId 许可证 ID
   * @returns 是否删除成功
   */
  deleteLicense = async (licenseId: string): Promise<boolean> => {
    const result = await serverDB
      .delete(license)
      .where(eq(license.id, licenseId))
      .returning();

    return result.length > 0;
  };

  /**
   * 获取所有许可证列表
   * @returns 所有许可证列表，包含关联的组织和版本信息
   */
  getAllLicenses = async (): Promise<LicenseWithRelations[]> => {
    const result = await serverDB.query.license.findMany({
      with: {
        organization: true,
        version: true,
      },
      orderBy: [license.createdAt],
    });

    return result;
  };
}
