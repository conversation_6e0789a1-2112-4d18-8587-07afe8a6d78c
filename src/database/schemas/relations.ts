import { relations } from 'drizzle-orm';

import { feature } from './feature';
import { license } from './license';
import { organization } from './organization';
import { version, versionFeature } from './version';

// LICENSE 表的关联关系
export const licenseRelations = relations(license, ({ one }) => ({
  // 一个 License 关联一个组织 (1:1)
  organization: one(organization, {
    fields: [license.organizationId],
    references: [organization.id],
  }),

  // 一个 License 关联一个版本 (1:1)
  version: one(version, {
    fields: [license.versionId],
    references: [version.id],
  }),
}));

// organization 表的关联关系
export const organizationRelations = relations(organization, ({ one }) => ({
  // 一个组织关联一个 License (1:1)
  license: one(license, {
    fields: [organization.licenseId],
    references: [license.id],
  }),
}));

// FEATURE 表的关联关系
export const featureRelations = relations(feature, ({ many }) => ({
  // 一个功能可以被多个 Version 关联 (1:N through VERSION_FEATURE)
  versionFeatures: many(versionFeature),
}));

// VERSION 表的关联关系
export const versionRelations = relations(version, ({ many }) => ({
  // 一个版本可以关联多个功能 (1:N through VERSION_FEATURE)
  versionFeatures: many(versionFeature),
}));

// VERSION_FEATURE 关联表的关系
export const versionFeatureRelations = relations(versionFeature, ({ one }) => ({
  // 关联到具体的 Version (N:1)
  version: one(version, {
    fields: [versionFeature.versionId],
    references: [version.id],
  }),

  // 关联到具体的 Feature (N:1)
  feature: one(feature, {
    fields: [versionFeature.featureId],
    references: [feature.id],
  }),
}));
