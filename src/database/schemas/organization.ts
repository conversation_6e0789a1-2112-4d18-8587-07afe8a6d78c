import { idGenerator } from '@/database/utils/idGenerator';
import { jsonb, pgTable, text } from 'drizzle-orm/pg-core';
import { timestamps } from './_helpers';

// 组织信息表
export const organization = pgTable('organization', {
  id: text('id')
    .$defaultFn(() => idGenerator('organization'))
    .primaryKey(), // 主键，自增
  chineseName: text('chinese_name').notNull(), // 组织中文名称
  englishName: text('english_name').notNull(), // 组织英文名称
  logo: text('logo'), // 组织logo
  scale: text('scale'), // 组织规模
  industry: text('industry'), // 组织行业
  country: text('country'), // 组织国家
  description: text('description'), // 组织描述
  assigner: jsonb('assigner'), // 对接人信息
  lobeOwner: jsonb('lobe_owner'), // Lobe 侧负责人信息
  licenseId: text('license_id'), // 许可证ID

  ...timestamps,
});

export type Organization = typeof organization.$inferSelect;
export type InsertOrganization = typeof organization.$inferInsert;
