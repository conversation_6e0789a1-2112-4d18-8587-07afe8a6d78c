import { boolean, pgTable, text, timestamp } from 'drizzle-orm/pg-core';

import { timestamps } from './_helpers';
import { idGenerator } from '../utils/idGenerator';
import { version } from './version';
import { organization } from './organization';

// 授权信息表
export const license = pgTable('license', {
  id: text('id')
    .$defaultFn(() => idGenerator('license'))
    .primaryKey(), // 主键，以 business 或 enterprise 开头，后跟 hash 值
  organizationId: text('organization_id')
    .notNull()
    .references(() => organization.id), // 组织ID
  versionId: text('version_id')
    .notNull()
    .references(() => version.id), // 许可证约束ID
  startTime: timestamp('start_time').notNull(), // 生效时间
  endTime: timestamp('end_time').notNull(), // 失效时间
  enabled: boolean('enabled').notNull().default(true), // 是否启用

  ...timestamps,
});

// TypeScript 类型定义
export type License = typeof license.$inferSelect;
export type InsertLicense = typeof license.$inferInsert;
