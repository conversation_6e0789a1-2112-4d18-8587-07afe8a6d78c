import { boolean, integer, pgTable, text, unique } from 'drizzle-orm/pg-core';

import { idGenerator } from '../utils/idGenerator';
import { timestamps } from './_helpers';
import { feature } from './feature';

// 版本表 - 将原来的约束信息抽象为版本概念
export const version = pgTable('version', {
  id: text('id')
    .$defaultFn(() => idGenerator('version'))
    .primaryKey(), // 主键，以 version 开头，后跟 hash 值
  name: text('name').notNull(), // 版本名称，如 "v1.0.0", "企业版2024Q1"
  description: text('description'), // 版本描述
  maxUsers: integer('max_users'), // 最大用户数限制
  maxVersion: text('max_version'), // 最大版本号
  minVersion: text('min_version'), // 最低版本号
  isActive: boolean('is_active').notNull().default(true), // 版本是否有效

  ...timestamps,
});

// 版本功能关联表 - 定义每个版本包含的功能权限
export const versionFeature = pgTable(
  'version_feature',
  {
    versionId: text('version_id')
      .notNull()
      .references(() => version.id), // 版本ID
    featureId: text('feature_id')
      .notNull()
      .references(() => feature.id), // 功能ID (改为text类型以匹配FEATURE表)
    grantType: text('grant_type').notNull(), // 授权类型: 'grant'(授予) 或 'revoke'(撤销)

    ...timestamps,
  },
  (t) => [unique('version_feature_unique').on(t.versionId, t.featureId)]
);

// TypeScript 类型定义
export type Version = typeof version.$inferSelect;
export type InsertVersion = typeof version.$inferInsert;

export type VersionFeature = typeof versionFeature.$inferSelect;
export type InsertVersionFeature = typeof versionFeature.$inferInsert;
