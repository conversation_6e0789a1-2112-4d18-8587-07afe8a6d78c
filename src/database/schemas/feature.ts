import { idGenerator } from '@/database/utils/idGenerator';
import { pgEnum, pgTable, text } from 'drizzle-orm/pg-core';
import { timestamps } from './_helpers';

// 枚举：所属系统
export const belongToEnum = pgEnum('belongTo', ['lobe', 'admin']);

// 功能表
export const feature = pgTable('feature', {
  id: text('id')
    .$defaultFn(() => idGenerator('feature'))
    .primaryKey(), // 主键，自增
  name: text('name').notNull(), // 功能名称
  code: text('code').notNull().unique(), // 功能代号（唯一），与 featureFlag 的 key 对应
  category: text('category'), // 功能分类: chat, admin, model, plugin, analytics
  description: text('description'), // 功能描述
  belongTo: belongToEnum('belong_to'), // 所属系统: lobe, admin

  ...timestamps,
});

export type Feature = typeof feature.$inferSelect;
export type InsertFeature = typeof feature.$inferInsert;
