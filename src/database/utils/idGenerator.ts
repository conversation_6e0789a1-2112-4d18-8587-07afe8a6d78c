import { createNanoId } from '@/utils/uuid';

const prefixes = {
  license: 'lic',
  feature: 'feat',
  organization: 'org',
  constraint: 'cons',
  version: 'ver',
  version_feature: 'vf',
} as const;

export const idGenerator = (namespace: keyof typeof prefixes, size = 12) => {
  const hash = createNanoId(size);
  const prefix = prefixes[namespace];

  if (!prefix)
    throw new Error(`Invalid namespace: ${namespace}, please check your code.`);

  return `${prefix}_${hash()}`;
};
