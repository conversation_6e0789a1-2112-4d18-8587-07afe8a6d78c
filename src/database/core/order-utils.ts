import { asc, desc } from 'drizzle-orm';
import { SortOptions } from '@/types';
import { PgTableWithColumns, TableConfig } from 'drizzle-orm/pg-core';

/**
 * 生成排序条件
 * @param sort 排序选项
 * @param table 表对象
 * @returns 排序条件数组
 */
export const generateOrderBy = <T extends TableConfig>(
  sort: SortOptions | undefined,
  table: PgTableWithColumns<T>
): Array<ReturnType<typeof asc> | ReturnType<typeof desc>> => {
  if (!sort) {
    return [];
  }

  return Object.entries(sort)
    .filter(([_, value]) => value !== undefined)
    .map(([key, value]) =>
      value === 'ascend' ? asc(table[key]) : desc(table[key])
    );
};
