import { Pool as NeonPool, neonConfig } from '@neondatabase/serverless';
import { drizzle as neonDrizzle } from 'drizzle-orm/neon-serverless';
import ws from 'ws';

import { dbEnv } from '@/envs/db';
import * as schema from '@/database/schemas';

import { LobeConsoleDatabase } from '../type';

export const getDBInstance = (): LobeConsoleDatabase => {
  if (!dbEnv.DATABASE_URL) {
    throw new Error(
      ` \`KEY_VAULTS_SECRET\` is not set, please set it in your environment variables.

If you don't have it, please run \`openssl rand -base64 32\` to create one.
`
    );
  }

  const connectionString = dbEnv.DATABASE_URL;

  if (!connectionString) {
    throw new Error(
      `You are try to use database, but "DATABASE_URL" is not set correctly`
    );
  }

  if (process.env.MIGRATION_DB === '1') {
    // https://github.com/neondatabase/serverless/blob/main/CONFIG.md#websocketconstructor-typeof-websocket--undefined
    neonConfig.webSocketConstructor = ws;
  }

  const client = new NeonPool({ connectionString });
  return neonDrizzle(client, { schema });
};
