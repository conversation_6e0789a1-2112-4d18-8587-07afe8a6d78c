{"id": "70d725c3-437e-48bc-b4f3-1793644691ad", "prevId": "00000000-0000-0000-0000-000000000000", "version": "7", "dialect": "postgresql", "tables": {"public.feature": {"name": "feature", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "code": {"name": "code", "type": "text", "primaryKey": false, "notNull": true}, "category": {"name": "category", "type": "text", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "belong_to": {"name": "belong_to", "type": "belongTo", "typeSchema": "public", "primaryKey": false, "notNull": false}, "accessed_at": {"name": "accessed_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"feature_code_unique": {"name": "feature_code_unique", "nullsNotDistinct": false, "columns": ["code"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.license": {"name": "license", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "organization_id": {"name": "organization_id", "type": "text", "primaryKey": false, "notNull": true}, "version_id": {"name": "version_id", "type": "text", "primaryKey": false, "notNull": true}, "license_type": {"name": "license_type", "type": "text", "primaryKey": false, "notNull": true}, "lobe_owner": {"name": "lobe_owner", "type": "jsonb", "primaryKey": false, "notNull": true}, "start_time": {"name": "start_time", "type": "timestamp", "primaryKey": false, "notNull": true}, "end_time": {"name": "end_time", "type": "timestamp", "primaryKey": false, "notNull": true}, "enabled": {"name": "enabled", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "accessed_at": {"name": "accessed_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"license_version_id_version_id_fk": {"name": "license_version_id_version_id_fk", "tableFrom": "license", "tableTo": "version", "columnsFrom": ["version_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.organization": {"name": "organization", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "chinese_name": {"name": "chinese_name", "type": "text", "primaryKey": false, "notNull": true}, "english_name": {"name": "english_name", "type": "text", "primaryKey": false, "notNull": true}, "logo": {"name": "logo", "type": "text", "primaryKey": false, "notNull": false}, "scale": {"name": "scale", "type": "text", "primaryKey": false, "notNull": false}, "industry": {"name": "industry", "type": "text", "primaryKey": false, "notNull": false}, "country": {"name": "country", "type": "text", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "assigner": {"name": "assigner", "type": "jsonb", "primaryKey": false, "notNull": false}, "lobe_owner": {"name": "lobe_owner", "type": "jsonb", "primaryKey": false, "notNull": true}, "license_id": {"name": "license_id", "type": "text", "primaryKey": false, "notNull": false}, "accessed_at": {"name": "accessed_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.version": {"name": "version", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "max_users": {"name": "max_users", "type": "integer", "primaryKey": false, "notNull": false}, "max_version": {"name": "max_version", "type": "text", "primaryKey": false, "notNull": false}, "min_version": {"name": "min_version", "type": "text", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "accessed_at": {"name": "accessed_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.version_feature": {"name": "version_feature", "schema": "", "columns": {"version_id": {"name": "version_id", "type": "text", "primaryKey": false, "notNull": true}, "feature_id": {"name": "feature_id", "type": "text", "primaryKey": false, "notNull": true}, "grant_type": {"name": "grant_type", "type": "text", "primaryKey": false, "notNull": true}, "enabled": {"name": "enabled", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "accessed_at": {"name": "accessed_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"version_feature_version_id_version_id_fk": {"name": "version_feature_version_id_version_id_fk", "tableFrom": "version_feature", "tableTo": "version", "columnsFrom": ["version_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "version_feature_feature_id_feature_id_fk": {"name": "version_feature_feature_id_feature_id_fk", "tableFrom": "version_feature", "tableTo": "feature", "columnsFrom": ["feature_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"version_feature_unique": {"name": "version_feature_unique", "nullsNotDistinct": false, "columns": ["version_id", "feature_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"public.belongTo": {"name": "belongTo", "schema": "public", "values": ["lobe", "admin"]}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}