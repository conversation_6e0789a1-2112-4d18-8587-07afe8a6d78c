'use client';

import { Icon } from '@lobehub/ui';
import { Skeleton } from 'antd';
import { css, cx } from 'antd-style';
import { ChevronDown, ChevronUp } from 'lucide-react';
import dynamic from 'next/dynamic';
import { useState } from 'react';
import { Flexbox } from 'react-layout-kit';

import { ERROR_MESSAGES } from '@/const/error';

const container = css`
  pre.shiki {
    padding: 8px !important;
  }
`;

const Highlighter = dynamic(() => import('@lobehub/ui/es/Highlighter'), {
  loading: () => <Skeleton avatar={false} title={false} />,
  ssr: false,
});

const Description = ({
  message,
  status,
}: {
  message: string;
  status: number;
}) => {
  const [show, setShow] = useState(false);
  return (
    <Flexbox gap={8}>
      {ERROR_MESSAGES[status as keyof typeof ERROR_MESSAGES] ||
        ERROR_MESSAGES[500]}
      <Flexbox
        gap={4}
        horizontal
        onClick={() => {
          setShow(!show);
        }}
        style={{ cursor: 'pointer', fontSize: 12 }}
      >
        错误详情 <Icon icon={show ? ChevronUp : ChevronDown} />
      </Flexbox>
      <Highlighter
        className={cx(container)}
        language={'text'}
        style={{ display: show ? undefined : 'none', maxHeight: 80 }}
      >
        {message}
      </Highlighter>
    </Flexbox>
  );
};

export default Description;
