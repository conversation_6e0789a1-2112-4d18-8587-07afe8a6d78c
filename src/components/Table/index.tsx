import { SortOrder } from '@/types';
import {
  ParamsType,
  ProTable,
  ProTableProps,
} from '@ant-design/pro-components';
import { ActionIcon } from '@lobehub/ui';
import { ConfigProvider } from 'antd';
import { createStyles, useResponsive } from 'antd-style';
import { ChevronDownIcon, ChevronUpIcon } from 'lucide-react';
import { parseAsInteger, useQueryState } from 'nuqs';
import { ReactElement, memo } from 'react';

const useStyles = createStyles(({ css, token, prefixCls, responsive }) => ({
  tableMobile: css`
    .${prefixCls}-pro-table-list-toolbar {
      padding-inline: 24px;
      background: ${token.colorBgContainer};
      ${responsive.mobile} {
        display: none;
      }
    }

    .${prefixCls}-form-item.${prefixCls}-pro-query-filter-actions {
      .${prefixCls}-row.${prefixCls}-form-item-row:has(.${prefixCls}-col.${prefixCls}-form-item-control) {
        > .${prefixCls}-col.${prefixCls}-form-item-label {
          display: none;
        }
      }
    }
  `,
}));

interface TableProps<
  DataType extends Record<string, unknown>,
  Params extends ParamsType = ParamsType,
  ValueType = 'text',
> extends Omit<
    ProTableProps<DataType, Params, ValueType>,
    'Summary' | 'request'
  > {
  defaultPageSize?: number;
  request?: (
    params: Params,
    sort: Record<string, SortOrder>,
    filters: Record<string, unknown>
  ) => Promise<{
    data: DataType[];
    total: number;
    success: boolean;
  }>;
}

const Table = <
  DataType extends Record<string, unknown>,
  Params extends ParamsType = ParamsType,
  ValueType = 'text',
>({
  form,
  search,
  className,
  pagination,
  headerTitle,
  defaultPageSize = 10,
  request,
  ...rest
}: TableProps<DataType, Params, ValueType>) => {
  const { cx, styles, theme } = useStyles();
  const { mobile } = useResponsive();

  // 分页状态管理
  const [currentPage, setCurrentPage] = useQueryState(
    'current',
    parseAsInteger.withDefault(1).withOptions({ clearOnDefault: true })
  );
  const [pageSize, setPageSize] = useQueryState(
    'pageSize',
    parseAsInteger
      .withDefault(defaultPageSize)
      .withOptions({ clearOnDefault: true })
  );

  return (
    <ConfigProvider
      theme={{
        components: {
          Table: {
            headerBorderRadius: mobile ? 0 : theme.borderRadius,
          },
        },
      }}
    >
      <ProTable<DataType, Params, ValueType>
        className={cx(mobile && styles.tableMobile, className)}
        form={{
          layout: 'vertical',
          variant: theme.isDarkMode ? 'filled' : 'outlined',
          ...form,
        }}
        ghost
        headerTitle={mobile ? false : headerTitle}
        scroll={{ x: 'max-content' }}
        {...(request
          ? {
              request: async (params, originSort, filters) => {
                const sort = Object.entries(originSort).reduce(
                  (acc, [key, value]) => {
                    if (!value) return acc;

                    acc[key] = value;

                    return acc;
                  },
                  {} as Record<string, SortOrder>
                );

                return request(params, sort, filters);
              },
            }
          : {})}
        search={
          search === false
            ? false
            : {
                collapseRender: (collapse: boolean) => (
                  <ActionIcon
                    icon={collapse ? ChevronDownIcon : ChevronUpIcon}
                    size={'small'}
                  />
                ),
                style: {
                  background: theme.colorBgContainer,
                  padding: mobile ? 16 : 0,
                },
                ...search,
              }
        }
        {...rest}
        pagination={
          pagination === false
            ? false
            : {
                current: currentPage,
                onChange: (page) => setCurrentPage(page),
                onShowSizeChange: (current, size) => {
                  setCurrentPage(current);
                  setPageSize(size);
                },
                pageSize,
                showSizeChanger: true,
                size: mobile ? 'small' : undefined,
                style: {
                  paddingInline: mobile ? 16 : 0,
                },
                ...pagination,
              }
        }
      />
    </ConfigProvider>
  );
};

export default Table as <
  DataType extends Record<string, unknown>,
  Params extends ParamsType = ParamsType,
  ValueType = 'text',
>(
  props: TableProps<DataType, Params, ValueType>
) => ReactElement;
