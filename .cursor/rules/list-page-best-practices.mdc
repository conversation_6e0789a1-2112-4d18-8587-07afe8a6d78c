---
description: 列表页面开发最佳实践
alwaysApply: false
---
# 列表页面开发最佳实践

> 基于 Lobe Console 功能管理页面的开发经验总结

## 📋 概述

本文档总结了在 Lobe Console 项目中开发列表页面的最佳实践，包括架构设计、代码组织、性能优化等方面的经验。这些实践经过实际项目验证，能够显著提高开发效率和代码质量。

## 🏗️ 核心架构原则

### 1. 简化架构模式

**❌ 避免过度抽象**
```typescript
// 不推荐：复杂的自定义 Hook
const useEntityManagement = () => {
  // 大量的状态管理和业务逻辑
  const [entities, setEntities] = useState([]);
  const [loading, setLoading] = useState(false);
  // ... 100+ 行代码
};
```

**✅ 推荐：直接集成**
```typescript
// 推荐：简洁的组件内逻辑
const EntityListPage = () => {
  const { message } = App.useApp();
  const actionRef = useRef<ActionType>(null);
  const [editId, setEditId] = useState<number | undefined>();
  const [isModalOpen, setIsModalOpen] = useState(false);

  // 简单直接的操作处理
};
```

### 2. 状态管理最小化

只保留必要的本地状态：
- Modal 显示状态
- 编辑项 ID
- 其他临时 UI 状态

```typescript
// 最小化状态管理
const [editId, setEditId] = useState<number | undefined>();
const [isModalOpen, setIsModalOpen] = useState(false);

// 使用 ActionRef 控制表格
const actionRef = useRef<ActionType>(null);
```

## 🔌 tRPC 集成最佳实践

### 1. 直接调用模式

**✅ 推荐做法**
```typescript
// 直接使用 tRPC 客户端
const handleDelete = async (id: number) => {
  const { success } = await trpcClient.entity.deleteEntity.mutate(id);

  if (success) {
    message.success('删除成功');
    actionRef.current?.reload();
  } else {
    message.error('删除失败');
  }
};
```

### 2. 数据获取模式

```typescript
// 使用 ProTable 的 request 属性
<Table
  actionRef={actionRef}
  request={async (params) => {
    const { data } = await trpcClient.entity.queryEntities.query({
      page: params.current,
      pageSize: params.pageSize,
      // 其他查询参数
    });

    return {
      data: data.data,
      total: data.total,
      success: true,
    };
  }}
/>
```

### 3. 统一错误处理

```typescript
const { message } = App.useApp();

// 统一的成功/失败处理模式
const handleOperation = async (operation: () => Promise<{ success: boolean }>) => {
  const { success } = await operation();

  if (success) {
    message.success('操作成功');
    actionRef.current?.reload();
  } else {
    message.error('操作失败');
  }
};
```

## 📝 表单组件最佳实践

### 1. Modal 表单结构

```typescript
interface EntityFormModalProps {
  open: boolean;
  entityId?: number;  // 使用 ID 而不是完整对象
  onCancel: () => void;
  onSuccess: () => void;  // 成功回调
}

export const EntityFormModal: React.FC<EntityFormModalProps> = ({
  open,
  entityId,
  onCancel,
  onSuccess,
}) => {
  const { message } = App.useApp();
  const [form] = Form.useForm();

  const handleSubmit = async () => {
    const values = await form.validateFields();

    const method = entityId
      ? trpcClient.entity.updateEntity.mutate
      : trpcClient.entity.createEntity.mutate;

    const { success } = await method(values);

    if (success) {
      message.success(entityId ? '编辑成功' : '添加成功');
      onSuccess();
    }
  };

  return (
    <Modal
      destroyOnHidden
      title={entityId ? '编辑实体' : '添加实体'}
      open={open}
      onOk={handleSubmit}
      onCancel={onCancel}
      width={600}
    >
      <ProForm
        submitter={false}
        form={form}
        request={async () => {
          if (!entityId) return {};

          const { data } = await trpcClient.entity.getEntity.query(entityId);
          return data;
        }}
      >
        {/* 表单字段 */}
      </ProForm>
    </Modal>
  );
};
```

### 2. ProForm 使用技巧

```typescript
// 使用 ProForm 的 request 属性自动处理编辑数据
<ProForm
  submitter={false}  // 禁用内置提交按钮
  form={form}
  request={async () => {
    if (!editId) return {};

    const { data } = await trpcClient.entity.getEntity.query(editId);
    return data;
  }}
>
  <ProFormText name="name" label="名称" rules={[{ required: true }]} />
  <ProFormText name="code" label="代号" rules={[{ required: true }]} />
  <ProFormSelect
    name="category"
    label="分类"
    options={CATEGORIES.map(cat => ({ label: cat, value: cat }))}
  />
  <ProFormTextArea name="description" label="描述" />
</ProForm>
```

## 📊 表格配置最佳实践

### 1. 列定义优化

```typescript
const columns: ProColumns<Entity>[] = [
  {
    title: 'ID',
    dataIndex: 'id',
    width: 80,
    search: false,  // 不参与搜索
  },
  {
    title: '名称',
    dataIndex: 'name',
    ellipsis: true,
    copyable: true,  // 支持复制
  },
  {
    title: '代号',
    dataIndex: 'code',
    width: 150,
    ellipsis: true,
    copyable: true,
  },
  {
    title: '分类',
    dataIndex: 'category',
    width: 120,
    valueType: 'select',
    fieldProps: {
      options: CATEGORIES.map(cat => ({ label: cat, value: cat })),
    },
    render: (text) => <Tag color="blue">{text}</Tag>,
  },
  {
    title: '描述',
    dataIndex: 'description',
    ellipsis: true,
    hideInSearch: true,  // 隐藏搜索
  },
  {
    title: '创建时间',
    dataIndex: 'createdAt',
    width: 160,
    search: false,
    sorter: true,
    valueType: 'dateTime',  // 使用内置类型
  },
  {
    title: '操作',
    valueType: 'option',
    width: 120,
    fixed: 'right',
    render: (_, record) => (
      <Space size="small">
        <ActionIcon
          icon={Edit3Icon}
          size="small"
          onClick={() => handleEdit(record.id)}
        />
        <Popconfirm
          title="确认删除"
          description='确定要删除该xxx吗？'
          onConfirm={() => handleDelete(record.id)}
          okText="确认"
          cancelText="取消"
        >
          <ActionIcon
            icon={DeleteIcon}
            size="small"
            style={{ color: '#ff4d4f' }}
          />
        </Popconfirm>
      </Space>
    ),
  },
];
```

### 2. 工具栏配置

```typescript
<Table
  columns={columns}
  actionRef={actionRef}
  toolBarRender={() => [
    <Button
      key="add"
      type="primary"
      icon={<PlusIcon size={16} />}
      onClick={() => handleOpenModal()}
    >
      添加实体
    </Button>,
    // 其他工具栏按钮
  ]}
  scroll={{ x: 1200 }}
  request={requestHandler}
/>
```

## 🎯 完整页面模板

```typescript
'use client';

import { App, Button, Popconfirm, Space, Tag } from 'antd';
import { ActionType, ProColumns } from '@ant-design/pro-components';
import { ActionIcon } from '@lobehub/ui';
import { DeleteIcon, Edit3Icon, PlusIcon } from 'lucide-react';
import { useRef, useState } from 'react';

import Table from '@/components/Table';
import { trpcClient } from '@/libs/trpc/client';
import { Entity } from '@/database/schemas';
import { EntityFormModal } from './components/EntityFormModal';

const EntityListPage = () => {
  const { message } = App.useApp();
  const actionRef = useRef<ActionType>(null);
  const [editId, setEditId] = useState<number | undefined>();
  const [isModalOpen, setIsModalOpen] = useState(false);

  // 删除操作
  const handleDelete = async (id: number) => {
    const { success } = await trpcClient.entity.deleteEntity.mutate(id);

    if (success) {
      message.success('删除成功');
      actionRef.current?.reload();
    } else {
      message.error('删除失败');
    }
  };

  // Modal 控制
  const handleOpenModal = (id?: number) => {
    setEditId(id);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setEditId(undefined);
    setIsModalOpen(false);
  };

  const handleSuccess = () => {
    handleCloseModal();
    actionRef.current?.reload();
  };

  // 表格列定义
  const columns: ProColumns<Entity>[] = [
    // ... 列定义
  ];

  return (
    <>
      <Table<Entity>
        columns={columns}
        rowKey="id"
        actionRef={actionRef}
        toolBarRender={() => [
          <Button
            key="add"
            type="primary"
            icon={<PlusIcon size={16} />}
            onClick={() => handleOpenModal()}
          >
            添加实体
          </Button>,
        ]}
        request={async (params) => {
          const { data } = await trpcClient.entity.queryEntities.query({
            page: params.current,
            pageSize: params.pageSize,
          });

          return {
            data: data.data,
            total: data.total,
            success: true,
          };
        }}
        scroll={{ x: 1200 }}
      />

      <EntityFormModal
        open={isModalOpen}
        entityId={editId}
        onCancel={handleCloseModal}
        onSuccess={handleSuccess}
      />
    </>
  );
};

export default EntityListPage;
```

## 🚀 性能优化建议

### 1. 避免不必要的重渲染

```typescript
// 使用 useCallback 包装事件处理函数
const handleDelete = useCallback(async (id: number) => {
  // 删除逻辑
}, []);

// 使用 useMemo 缓存列定义
const columns = useMemo(() => [
  // 列定义
], []);
```

### 2. 合理使用 ActionRef

```typescript
// 只在必要时刷新表格
if (success) {
  message.success('操作成功');
  actionRef.current?.reload();  // 而不是 refetch
}
```

### 3. 优化网络请求

```typescript
// 使用防抖进行搜索
const debouncedSearch = useDebounceFn(
  (value: string) => {
    actionRef.current?.reload();
  },
  { wait: 300 }
);
```

## 📁 文件组织结构

```
src/app/(frontend)/(main)/entities/
├── page.tsx                    # 主页面组件
├── components/
│   └── EntityFormModal.tsx     # 表单 Modal 组件
└── types/
    └── index.ts                # 类型定义
```

## ✅ 开发检查清单

### 功能完整性
- [ ] CRUD 操作完整实现
- [ ] 表单验证正确配置
- [ ] 错误处理统一处理
- [ ] 成功消息正确显示

### 用户体验
- [ ] 操作确认对话框
- [ ] Loading 状态显示
- [ ] 响应式设计适配
- [ ] 键盘快捷键支持

### 代码质量
- [ ] TypeScript 类型安全
- [ ] 组件职责单一
- [ ] 代码复用性良好
- [ ] 性能优化到位

### 测试验证
- [ ] 所有 CRUD 操作测试
- [ ] 边界情况处理
- [ ] 错误场景验证
- [ ] 用户交互测试

## 🎨 样式和主题

### 1. 使用项目统一样式

```typescript
import { createStyles } from 'antd-style';

const useStyles = createStyles(({ css, token }) => ({
  container: css`
    padding: ${token.padding}px;
    background: ${token.colorBgContainer};
  `,
}));
```

### 2. 响应式设计

```typescript
const { mobile } = useResponsive();

// 根据设备类型调整布局
<Table
  size={mobile ? 'small' : 'middle'}
  scroll={{ x: mobile ? 800 : 1200 }}
/>
```

## 🔧 调试和故障排除

### 1. 常见问题

**问题**: 表格数据不刷新
```typescript
// 解决方案：确保正确调用 reload
actionRef.current?.reload();
```

**问题**: 表单验证不生效
```typescript
// 解决方案：确保表单字段名称与数据字段匹配
<ProFormText name="entityName" />  // 确保与后端字段一致
```

### 2. 调试技巧

```typescript
// 添加调试日志
console.log('Table request params:', params);
console.log('API response:', data);

// 使用 React DevTools 检查组件状态
```

## 📚 相关资源

- [Ant Design Pro Components 文档](https://procomponents.ant.design/)
- [tRPC 官方文档](https://trpc.io/)
- [React Hook Form 文档](https://react-hook-form.com/)
- [Ant Design 设计规范](https://ant.design/docs/spec/introduce-cn)

## 🔄 版本更新记录

- **v1.0.0** (2024-12-19): 初始版本，基于功能管理页面开发经验
- 后续版本将根据实际使用反馈持续优化

---

> 💡 **提示**: 这个最佳实践文档会随着项目发展持续更新，建议定期查看最新版本。
# 列表页面开发最佳实践

> 基于 Lobe Console 功能管理页面的开发经验总结

## 📋 概述

本文档总结了在 Lobe Console 项目中开发列表页面的最佳实践，包括架构设计、代码组织、性能优化等方面的经验。这些实践经过实际项目验证，能够显著提高开发效率和代码质量。

## 🏗️ 核心架构原则

### 1. 简化架构模式

**❌ 避免过度抽象**
```typescript
// 不推荐：复杂的自定义 Hook
const useEntityManagement = () => {
  // 大量的状态管理和业务逻辑
  const [entities, setEntities] = useState([]);
  const [loading, setLoading] = useState(false);
  // ... 100+ 行代码
};
```

**✅ 推荐：直接集成**
```typescript
// 推荐：简洁的组件内逻辑
const EntityListPage = () => {
  const { message } = App.useApp();
  const actionRef = useRef<ActionType>(null);
  const [editId, setEditId] = useState<number | undefined>();
  const [isModalOpen, setIsModalOpen] = useState(false);

  // 简单直接的操作处理
};
```

### 2. 状态管理最小化

只保留必要的本地状态：
- Modal 显示状态
- 编辑项 ID
- 其他临时 UI 状态

```typescript
// 最小化状态管理
const [editId, setEditId] = useState<number | undefined>();
const [isModalOpen, setIsModalOpen] = useState(false);

// 使用 ActionRef 控制表格
const actionRef = useRef<ActionType>(null);
```

## 🔌 tRPC 集成最佳实践

### 1. 直接调用模式

**✅ 推荐做法**
```typescript
// 直接使用 tRPC 客户端
const handleDelete = async (id: number) => {
  const { success } = await trpcClient.entity.deleteEntity.mutate(id);

  if (success) {
    message.success('删除成功');
    actionRef.current?.reload();
  } else {
    message.error('删除失败');
  }
};
```

### 2. 数据获取模式

```typescript
// 使用 ProTable 的 request 属性
<Table
  actionRef={actionRef}
  request={async (params) => {
    const { data } = await trpcClient.entity.queryEntities.query({
      page: params.current,
      pageSize: params.pageSize,
      // 其他查询参数
    });

    return {
      data: data.data,
      total: data.total,
      success: true,
    };
  }}
/>
```

### 3. 统一错误处理

```typescript
const { message } = App.useApp();

// 统一的成功/失败处理模式
const handleOperation = async (operation: () => Promise<{ success: boolean }>) => {
  const { success } = await operation();

  if (success) {
    message.success('操作成功');
    actionRef.current?.reload();
  } else {
    message.error('操作失败');
  }
};
```

## 📝 表单组件最佳实践

### 1. Modal 表单结构

```typescript
interface EntityFormModalProps {
  open: boolean;
  entityId?: number;  // 使用 ID 而不是完整对象
  onCancel: () => void;
  onSuccess: () => void;  // 成功回调
}

export const EntityFormModal: React.FC<EntityFormModalProps> = ({
  open,
  entityId,
  onCancel,
  onSuccess,
}) => {
  const { message } = App.useApp();
  const [form] = Form.useForm();

  const handleSubmit = async () => {
    const values = await form.validateFields();

    const method = entityId
      ? trpcClient.entity.updateEntity.mutate
      : trpcClient.entity.createEntity.mutate;

    const { success } = await method(values);

    if (success) {
      message.success(entityId ? '编辑成功' : '添加成功');
      onSuccess();
    }
  };

  return (
    <Modal
      destroyOnHidden
      title={entityId ? '编辑实体' : '添加实体'}
      open={open}
      onOk={handleSubmit}
      onCancel={onCancel}
      width={600}
    >
      <ProForm
        submitter={false}
        form={form}
        request={async () => {
          if (!entityId) return {};

          const { data } = await trpcClient.entity.getEntity.query(entityId);
          return data;
        }}
      >
        {/* 表单字段 */}
      </ProForm>
    </Modal>
  );
};
```

### 2. ProForm 使用技巧

```typescript
// 使用 ProForm 的 request 属性自动处理编辑数据
<ProForm
  submitter={false}  // 禁用内置提交按钮
  form={form}
  request={async () => {
    if (!editId) return {};

    const { data } = await trpcClient.entity.getEntity.query(editId);
    return data;
  }}
>
  <ProFormText name="name" label="名称" rules={[{ required: true }]} />
  <ProFormText name="code" label="代号" rules={[{ required: true }]} />
  <ProFormSelect
    name="category"
    label="分类"
    options={CATEGORIES.map(cat => ({ label: cat, value: cat }))}
  />
  <ProFormTextArea name="description" label="描述" />
</ProForm>
```

## 📊 表格配置最佳实践

### 1. 列定义优化

```typescript
const columns: ProColumns<Entity>[] = [
  {
    title: 'ID',
    dataIndex: 'id',
    width: 80,
    search: false,  // 不参与搜索
  },
  {
    title: '名称',
    dataIndex: 'name',
    ellipsis: true,
    copyable: true,  // 支持复制
  },
  {
    title: '代号',
    dataIndex: 'code',
    width: 150,
    ellipsis: true,
    copyable: true,
  },
  {
    title: '分类',
    dataIndex: 'category',
    width: 120,
    valueType: 'select',
    fieldProps: {
      options: CATEGORIES.map(cat => ({ label: cat, value: cat })),
    },
    render: (text) => <Tag color="blue">{text}</Tag>,
  },
  {
    title: '描述',
    dataIndex: 'description',
    ellipsis: true,
    hideInSearch: true,  // 隐藏搜索
  },
  {
    title: '创建时间',
    dataIndex: 'createdAt',
    width: 160,
    search: false,
    sorter: true,
    valueType: 'dateTime',  // 使用内置类型
  },
  {
    title: '操作',
    valueType: 'option',
    width: 120,
    fixed: 'right',
    render: (_, record) => (
      <Space size="small">
        <ActionIcon
          icon={Edit3Icon}
          size="small"
          onClick={() => handleEdit(record.id)}
        />
        <Popconfirm
          title="确认删除"
          description='确定要删除该xxx吗？'
          onConfirm={() => handleDelete(record.id)}
          okText="确认"
          cancelText="取消"
        >
          <ActionIcon
            icon={DeleteIcon}
            size="small"
            style={{ color: '#ff4d4f' }}
          />
        </Popconfirm>
      </Space>
    ),
  },
];
```

### 2. 工具栏配置

```typescript
<Table
  columns={columns}
  actionRef={actionRef}
  toolBarRender={() => [
    <Button
      key="add"
      type="primary"
      icon={<PlusIcon size={16} />}
      onClick={() => handleOpenModal()}
    >
      添加实体
    </Button>,
    // 其他工具栏按钮
  ]}
  scroll={{ x: 1200 }}
  request={requestHandler}
/>
```

## 🎯 完整页面模板

```typescript
'use client';

import { App, Button, Popconfirm, Space, Tag } from 'antd';
import { ActionType, ProColumns } from '@ant-design/pro-components';
import { ActionIcon } from '@lobehub/ui';
import { DeleteIcon, Edit3Icon, PlusIcon } from 'lucide-react';
import { useRef, useState } from 'react';

import Table from '@/components/Table';
import { trpcClient } from '@/libs/trpc/client';
import { Entity } from '@/database/schemas';
import { EntityFormModal } from './components/EntityFormModal';

const EntityListPage = () => {
  const { message } = App.useApp();
  const actionRef = useRef<ActionType>(null);
  const [editId, setEditId] = useState<number | undefined>();
  const [isModalOpen, setIsModalOpen] = useState(false);

  // 删除操作
  const handleDelete = async (id: number) => {
    const { success } = await trpcClient.entity.deleteEntity.mutate(id);

    if (success) {
      message.success('删除成功');
      actionRef.current?.reload();
    } else {
      message.error('删除失败');
    }
  };

  // Modal 控制
  const handleOpenModal = (id?: number) => {
    setEditId(id);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setEditId(undefined);
    setIsModalOpen(false);
  };

  const handleSuccess = () => {
    handleCloseModal();
    actionRef.current?.reload();
  };

  // 表格列定义
  const columns: ProColumns<Entity>[] = [
    // ... 列定义
  ];

  return (
    <>
      <Table<Entity>
        columns={columns}
        rowKey="id"
        actionRef={actionRef}
        toolBarRender={() => [
          <Button
            key="add"
            type="primary"
            icon={<PlusIcon size={16} />}
            onClick={() => handleOpenModal()}
          >
            添加实体
          </Button>,
        ]}
        request={async (params) => {
          const { data } = await trpcClient.entity.queryEntities.query({
            page: params.current,
            pageSize: params.pageSize,
          });

          return {
            data: data.data,
            total: data.total,
            success: true,
          };
        }}
        scroll={{ x: 1200 }}
      />

      <EntityFormModal
        open={isModalOpen}
        entityId={editId}
        onCancel={handleCloseModal}
        onSuccess={handleSuccess}
      />
    </>
  );
};

export default EntityListPage;
```

## 🚀 性能优化建议

### 1. 避免不必要的重渲染

```typescript
// 使用 useCallback 包装事件处理函数
const handleDelete = useCallback(async (id: number) => {
  // 删除逻辑
}, []);

// 使用 useMemo 缓存列定义
const columns = useMemo(() => [
  // 列定义
], []);
```

### 2. 合理使用 ActionRef

```typescript
// 只在必要时刷新表格
if (success) {
  message.success('操作成功');
  actionRef.current?.reload();  // 而不是 refetch
}
```

### 3. 优化网络请求

```typescript
// 使用防抖进行搜索
const debouncedSearch = useDebounceFn(
  (value: string) => {
    actionRef.current?.reload();
  },
  { wait: 300 }
);
```

## 📁 文件组织结构

```
src/app/(frontend)/(main)/entities/
├── page.tsx                    # 主页面组件
├── components/
│   └── EntityFormModal.tsx     # 表单 Modal 组件
└── types/
    └── index.ts                # 类型定义
```

## ✅ 开发检查清单

### 功能完整性
- [ ] CRUD 操作完整实现
- [ ] 表单验证正确配置
- [ ] 错误处理统一处理
- [ ] 成功消息正确显示

### 用户体验
- [ ] 操作确认对话框
- [ ] Loading 状态显示
- [ ] 响应式设计适配
- [ ] 键盘快捷键支持

### 代码质量
- [ ] TypeScript 类型安全
- [ ] 组件职责单一
- [ ] 代码复用性良好
- [ ] 性能优化到位

### 测试验证
- [ ] 所有 CRUD 操作测试
- [ ] 边界情况处理
- [ ] 错误场景验证
- [ ] 用户交互测试

## 🎨 样式和主题

### 1. 使用项目统一样式

```typescript
import { createStyles } from 'antd-style';

const useStyles = createStyles(({ css, token }) => ({
  container: css`
    padding: ${token.padding}px;
    background: ${token.colorBgContainer};
  `,
}));
```

### 2. 响应式设计

```typescript
const { mobile } = useResponsive();

// 根据设备类型调整布局
<Table
  size={mobile ? 'small' : 'middle'}
  scroll={{ x: mobile ? 800 : 1200 }}
/>
```

## 🔧 调试和故障排除

### 1. 常见问题

**问题**: 表格数据不刷新
```typescript
// 解决方案：确保正确调用 reload
actionRef.current?.reload();
```

**问题**: 表单验证不生效
```typescript
// 解决方案：确保表单字段名称与数据字段匹配
<ProFormText name="entityName" />  // 确保与后端字段一致
```

### 2. 调试技巧

```typescript
// 添加调试日志
console.log('Table request params:', params);
console.log('API response:', data);

// 使用 React DevTools 检查组件状态
```

## 📚 相关资源

- [Ant Design Pro Components 文档](https://procomponents.ant.design/)
- [tRPC 官方文档](https://trpc.io/)
- [React Hook Form 文档](https://react-hook-form.com/)
- [Ant Design 设计规范](https://ant.design/docs/spec/introduce-cn)

## 🔄 版本更新记录

- **v1.0.0** (2024-12-19): 初始版本，基于功能管理页面开发经验
- 后续版本将根据实际使用反馈持续优化

---

> 💡 **提示**: 这个最佳实践文档会随着项目发展持续更新，建议定期查看最新版本。
